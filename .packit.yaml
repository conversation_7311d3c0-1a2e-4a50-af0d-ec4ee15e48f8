# See the documentation for more information:
# https://packit.dev/docs/configuration/

specfile_path: rpm/deepin-launcher.spec

# add or remove files that should be synced
synced_files:
    - rpm/deepin-launcher.spec
    - .packit.yaml

upstream_package_name: dde-launcher
# downstream (Fedora) RPM package name
downstream_package_name: deepin-launcher

actions:
  fix-spec-file: |
    bash -c "sed -i -r \"s/Version:(\s*)\S*/Version:\1${PACKIT_PROJECT_VERSION}/\" rpm/deepin-launcher.spec"
  post-upstream-clone: |
    cp rpm/dde-launcher.spec rpm/deepin-launcher.spec
