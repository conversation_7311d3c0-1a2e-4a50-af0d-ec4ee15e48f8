/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp -c DBusMenuManager -p dbusmenumanager menumanager.xml
 *
 * qdbusxml2cpp is Copyright (C) 2015 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#ifndef DBUSMENUMANAGER_H
#define DBUSMENUMANAGER_H

#include <QtCore/QObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>
#include <QtDBus/QtDBus>

/*
 * Proxy class for interface com.deepin.menu.Manager
 */
class DBusMenuManager: public QDBusAbstractInterface
{
    Q_OBJECT
    Q_SLOT void __propertyChanged__(const QDBusMessage& msg)
    {
        QList<QVariant> arguments = msg.arguments();
        if (3 != arguments.count())
            return;
        QString interfaceName = msg.arguments().at(0).toString();
        if (interfaceName !="com.deepin.menu.Manager")
            return;
        QVariantMap changedProps = qdbus_cast<QVariantMap>(arguments.at(1).value<QDBusArgument>());
        foreach(const QString &prop, changedProps.keys()) {
        const QMetaObject* self = metaObject();
            for (int i=self->propertyOffset(); i < self->propertyCount(); ++i) {
                QMetaProperty p = self->property(i);
                if (p.name() == prop) {
                Q_EMIT p.notifySignal().invoke(this);
                }
            }
        }
   }
public:
    static inline const char *staticInterfaceName()
    { return "com.deepin.menu.Manager"; }

public:
    explicit DBusMenuManager(QObject *parent = 0);

    ~DBusMenuManager();

public Q_SLOTS: // METHODS
    inline QDBusPendingReply<QDBusObjectPath> RegisterMenu()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("RegisterMenu"), argumentList);
    }

    inline QDBusPendingReply<> UnregisterMenu(const QString &menuObjectPath)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(menuObjectPath);
        return asyncCallWithArgumentList(QStringLiteral("UnregisterMenu"), argumentList);
    }

Q_SIGNALS: // SIGNALS
};

namespace com {
  namespace deepin {
    namespace menu {
      typedef ::DBusMenuManager Manager;
    }
  }
}
#endif
