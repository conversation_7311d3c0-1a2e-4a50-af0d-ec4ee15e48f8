/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp -c DBusLauncher -p dbuslauncher com.deepin.dde.daemon.Launcher.xml
 *
 * qdbusxml2cpp is Copyright (C) 2015 Digia Plc and/or its subsidiary(-ies).
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#include "dbuslauncher.h"

/*
 * Implementation of interface class DBusLauncher
 */

DBusLauncher::DBusLauncher(QObject *parent)
    : QDBusAbstractInterface("com.deepin.dde.daemon.Launcher", "/com/deepin/dde/daemon/Launcher", staticInterfaceName(), QDBusConnection::sessionBus(), parent)
{
    CategoryInfo::registerMetaType();
    FrequencyInfo::registerMetaType();
    ItemInfo::registerMetaType();
    InstalledTimeInfo::registerMetaType();

    QDBusConnection::sessionBus().connect(this->service(), this->path(), "org.freedesktop.DBus.Properties",  "PropertiesChanged","sa{sv}as", this, SLOT(__propertyChanged__(QDBusMessage)));
}

DBusLauncher::~DBusLauncher()
{
    QDBusConnection::sessionBus().disconnect(service(), path(), "org.freedesktop.DBus.Properties",  "PropertiesChanged",  "sa{sv}as", this, SLOT(propertyChanged(QDBusMessage)));
}

