/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp -c DBusMenu -p dbusmenu menu.xml
 *
 * qdbusxml2cpp is Copyright (C) 2015 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#ifndef DBUSMENU_H
#define DBUSMENU_H

#include <QtCore/QObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>
#include <QtDBus/QtDBus>

/*
 * Proxy class for interface com.deepin.menu.Menu
 */
class DBusMenu: public QDBusAbstractInterface
{
    Q_OBJECT
    Q_SLOT void __propertyChanged__(const QDBusMessage& msg)
    {
        QList<QVariant> arguments = msg.arguments();
        if (3 != arguments.count())
            return;
        QString interfaceName = msg.arguments().at(0).toString();
        if (interfaceName !="com.deepin.menu.Menu")
            return;
        QVariantMap changedProps = qdbus_cast<QVariantMap>(arguments.at(1).value<QDBusArgument>());
        foreach(const QString &prop, changedProps.keys()) {
        const QMetaObject* self = metaObject();
            for (int i=self->propertyOffset(); i < self->propertyCount(); ++i) {
                QMetaProperty p = self->property(i);
                if (p.name() == prop) {
                Q_EMIT p.notifySignal().invoke(this);
                }
            }
        }
   }
public:
    static inline const char *staticInterfaceName()
    { return "com.deepin.menu.Menu"; }

public:
    DBusMenu(const QString &service, const QString &path, const QDBusConnection &connection, QObject *parent = 0);

    ~DBusMenu();

public Q_SLOTS: // METHODS
    inline QDBusPendingReply<> SetItemActivity(const QString &itemId, bool isActive)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(itemId) << QVariant::fromValue(isActive);
        return asyncCallWithArgumentList(QStringLiteral("SetItemActivity"), argumentList);
    }

    inline QDBusPendingReply<> SetItemChecked(const QString &itemId, bool checked)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(itemId) << QVariant::fromValue(checked);
        return asyncCallWithArgumentList(QStringLiteral("SetItemChecked"), argumentList);
    }

    inline QDBusPendingReply<> SetItemText(const QString &itemId, const QString &text)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(itemId) << QVariant::fromValue(text);
        return asyncCallWithArgumentList(QStringLiteral("SetItemText"), argumentList);
    }

    inline QDBusPendingReply<> ShowMenu(const QString &menuJsonContent)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(menuJsonContent);
        return asyncCallWithArgumentList(QStringLiteral("ShowMenu"), argumentList);
    }

Q_SIGNALS: // SIGNALS
    void ItemInvoked(const QString &itemId, bool checked);
    void MenuUnregistered();
};

namespace com {
  namespace deepin {
    namespace menu {
      typedef ::DBusMenu Menu;
    }
  }
}
#endif
