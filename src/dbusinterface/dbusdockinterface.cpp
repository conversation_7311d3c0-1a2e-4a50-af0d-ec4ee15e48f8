/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp -c DBusDockInterface -p cadbusdockinterface.h:dbusdockinterface.cpp dbusdock.xml
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#include "dbusdockinterface.h"

/*
 * Implementation of interface class DBusDockInterface
 */

DBusDockInterface::DBusDockInterface(QObject *parent)
    : QDBusAbstractInterface("com.deepin.dde.Dock", "/com/deepin/dde/Dock", staticInterfaceName(), QDBusConnection::sessionBus(), parent)
{
    QDBusConnection::sessionBus().connect(this->service(), this->path(), "org.freedesktop.DBus.Properties",  "PropertiesChanged","sa{sv}as", this, SLOT(__propertyChanged__(QDBusMessage)));
}

DBusDockInterface::~DBusDockInterface()
{
}

