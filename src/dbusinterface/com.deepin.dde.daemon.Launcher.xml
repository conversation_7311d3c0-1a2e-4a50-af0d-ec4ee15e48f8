     <interface name="com.deepin.dde.daemon.Launcher">
          <method name="GetAllCategoryInfos">
               <arg type="a(sxas)" direction="out"></arg>
               <annotation name="org.qtproject.QtDBus.QtTypeName.Out0" value="CategoryInfoList"/>
          </method>
          <method name="GetAllFrequency">
               <arg type="a(st)" direction="out"></arg>
               <annotation name="org.qtproject.QtDBus.QtTypeName.Out0" value="FrequencyInfoList"/>
          </method>
          <method name="GetAllItemInfos">
               <arg type="a(ssssxx)" direction="out"></arg>
               <annotation name="org.qtproject.QtDBus.QtTypeName.Out0" value="ItemInfoList"/>
          </method>
          <method name="GetAllNewInstalledApps">
               <arg type="as" direction="out"></arg>
          </method>
          <method name="GetAllTimeInstalled">
               <arg type="a(sx)" direction="out"></arg>
               <annotation name="org.qtproject.QtDBus.QtTypeName.Out0" value="InstalledTimeInfoList"/>
          </method>
          </method>
          <method name="GetCategoryInfo">
               <arg type="x" direction="in"></arg>
               <arg type="(sxas)" direction="out"></arg>
               <annotation name="org.qtproject.QtDBus.QtTypeName.Out0" value="CategoryInfo"/>
          </method>
          <method name="GetItemInfo">
               <arg type="s" direction="in"></arg>
               <arg type="(ssssxx)" direction="out"></arg>
               <annotation name="org.qtproject.QtDBus.QtTypeName.Out0" value="ItemInfo"/>
          </method>
          <method name="IsItemOnDesktop">
               <arg type="s" direction="in"></arg>
               <arg type="b" direction="out"></arg>
          </method>
          <method name="MarkLaunched">
               <arg type="s" direction="in"></arg>
          </method>
          <method name="RecordFrequency">
               <arg type="s" direction="in"></arg>
          </method>
          <method name="RecordRate">
               <arg type="s" direction="in"></arg>
          </method>
          <method name="RefreshItem">
               <arg type="s" direction="in"></arg>
               <arg type="(ssssxx)" direction="out"></arg>
               <annotation name="org.qtproject.QtDBus.QtTypeName.Out0" value="ItemInfo"/>
          </method>
          <method name="RequestRemoveFromDesktop">
               <arg type="s" direction="in"></arg>
               <arg type="b" direction="out"></arg>
          </method>
          <method name="RequestSendToDesktop">
               <arg type="s" direction="in"></arg>
               <arg type="b" direction="out"></arg>
          </method>
          <method name="RequestUninstall">
               <arg type="s" direction="in"></arg>
               <arg type="b" direction="in"></arg>
          </method>
          <method name="Search">
               <arg type="s" direction="in"></arg>
          </method>
          <signal name="ItemChanged">
               <arg type="s"></arg>
               <arg type="(ssssxx)"></arg>
               <arg type="x"></arg>
               <annotation name="org.qtproject.QtDBus.QtTypeName.In1" value="ItemInfo"/>
          </signal>
          <signal name="UninstallSuccess">
               <arg type="s"></arg>
          </signal>
          <signal name="UninstallFailed">
               <arg type="s"></arg>
               <arg type="s"></arg>
          </signal>
          <signal name="SendToDesktopSuccess">
               <arg type="s"></arg>
          </signal>
          <signal name="SendToDesktopFailed">
               <arg type="s"></arg>
               <arg type="s"></arg>
          </signal>
          <signal name="RemoveFromDesktopSuccess">
               <arg type="s"></arg>
          </signal>
          <signal name="RemoveFromDesktopFailed">
               <arg type="s"></arg>
               <arg type="s"></arg>
          </signal>
          <signal name="SearchDone">
               <arg type="as"></arg>
          </signal>
          <signal name="NewAppLaunched">
               <arg type="s"></arg>
          </signal>
          <signal name="NewAppMarkedAsLaunched">
               <arg type="s"></arg>
          </signal>
     </interface>
