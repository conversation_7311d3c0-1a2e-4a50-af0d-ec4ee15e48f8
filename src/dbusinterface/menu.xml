<interface name="com.deepin.menu.Menu">
    <method name="ShowMenu">
     <arg direction="in" type="s" name="menuJsonContent" />\n 
    </method>\n 
    <method name="SetItemActivity">
     <arg direction="in" type="s" name="itemId" />\n 
     <arg direction="in" type="b" name="isActive" />\n 
    </method>\n 
    <method name="SetItemChecked">
     <arg direction="in" type="s" name="itemId" />\n 
     <arg direction="in" type="b" name="checked" />\n 
    </method>\n 
    <method name="SetItemText">
    <arg direction="in" type="s" name="itemId" />\n 
    <arg direction="in" type="s" name="text" />\n 
    </method>\n 
    <signal name="ItemInvoked">
    <arg direction="out" type="s" name="itemId" />\n 
    <arg direction="out" type="b" name="checked" />\n 
    </signal>\n 
    <signal name="MenuUnregistered" />\n
   </interface>\n