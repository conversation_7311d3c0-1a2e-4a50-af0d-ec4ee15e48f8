/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp -c DBusLauncher -p dbuslauncher com.deepin.dde.daemon.Launcher.xml
 *
 * qdbusxml2cpp is Copyright (C) 2015 Digia Plc and/or its subsidiary(-ies).
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#ifndef DBUSLAUNCHER_H_1456917835
#define DBUSLAUNCHER_H_1456917835

#include <QtCore/QObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>
#include <QtDBus/QtDBus>

#include "categoryinfo.h"
#include "frequencyinfo.h"
#include "iteminfo.h"
#include "installedtimeinfo.h"

/*
 * Proxy class for interface com.deepin.dde.daemon.Launcher
 */
class DBusLauncher: public QDBusAbstractInterface
{
    Q_OBJECT

    Q_SLOT void __propertyChanged__(const QDBusMessage& msg)
    {
        QList<QVariant> arguments = msg.arguments();
        if (3 != arguments.count())
            return;
        QString interfaceName = msg.arguments().at(0).toString();
        if (interfaceName !="com.deepin.dde.daemon.Launcher")
            return;
        QVariantMap changedProps = qdbus_cast<QVariantMap>(arguments.at(1).value<QDBusArgument>());
        foreach(const QString &prop, changedProps.keys()) {
        const QMetaObject* self = metaObject();
            for (int i=self->propertyOffset(); i < self->propertyCount(); ++i) {
                QMetaProperty p = self->property(i);
                if (p.name() == prop) {
 	            Q_EMIT p.notifySignal().invoke(this);
                }
            }
        }
   }
public:
    static inline const char *staticInterfaceName()
    { return "com.deepin.dde.daemon.Launcher"; }

public:
    explicit DBusLauncher(QObject *parent = 0);

    ~DBusLauncher();

    Q_PROPERTY(bool Fullscreen READ fullscreen NOTIFY FullscreenChanged)
    inline bool fullscreen() const
    { return qvariant_cast< bool >(property("Fullscreen")); }

    Q_PROPERTY(int DisplayMode READ displaymode NOTIFY DisplayModeChanged)
    inline int displaymode() const
    { return qvariant_cast< int >(property("DisplayMode")); }

public Q_SLOTS: // METHODS
    inline QDBusPendingReply<CategoryInfoList> GetAllCategoryInfos()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("GetAllCategoryInfos"), argumentList);
    }

    inline QDBusPendingReply<FrequencyInfoList> GetAllFrequency()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("GetAllFrequency"), argumentList);
    }

    inline QDBusPendingReply<ItemInfoList> GetAllItemInfos()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("GetAllItemInfos"), argumentList);
    }

    inline QDBusPendingReply<QStringList> GetAllNewInstalledApps()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("GetAllNewInstalledApps"), argumentList);
    }

    inline QDBusPendingReply<InstalledTimeInfoList> GetAllTimeInstalled()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("GetAllTimeInstalled"), argumentList);
    }

    inline QDBusPendingReply<CategoryInfo> GetCategoryInfo(qlonglong in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);
        return asyncCallWithArgumentList(QStringLiteral("GetCategoryInfo"), argumentList);
    }

    inline QDBusPendingReply<ItemInfo> GetItemInfo(const QString &in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);
        return asyncCallWithArgumentList(QStringLiteral("GetItemInfo"), argumentList);
    }

    inline QDBusPendingReply<bool> IsItemOnDesktop(const QString &in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);
        return asyncCallWithArgumentList(QStringLiteral("IsItemOnDesktop"), argumentList);
    }

    inline QDBusPendingReply<> MarkLaunched(const QString &in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);
        return asyncCallWithArgumentList(QStringLiteral("MarkLaunched"), argumentList);
    }

    inline QDBusPendingReply<> RecordFrequency(const QString &in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);
        return asyncCallWithArgumentList(QStringLiteral("RecordFrequency"), argumentList);
    }

    inline QDBusPendingReply<> RecordRate(const QString &in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);
        return asyncCallWithArgumentList(QStringLiteral("RecordRate"), argumentList);
    }

    inline QDBusPendingReply<bool> RequestRemoveFromDesktop(const QString &in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);
        return asyncCallWithArgumentList(QStringLiteral("RequestRemoveFromDesktop"), argumentList);
    }

    inline QDBusPendingReply<bool> RequestSendToDesktop(const QString &in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);
        return asyncCallWithArgumentList(QStringLiteral("RequestSendToDesktop"), argumentList);
    }

    inline QDBusPendingReply<> RequestUninstall(const QString &in0, bool in1)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0) << QVariant::fromValue(in1);
        return asyncCallWithArgumentList(QStringLiteral("RequestUninstall"), argumentList);
    }

    inline QDBusPendingReply<> Search(const QString &in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);
        return asyncCallWithArgumentList(QStringLiteral("Search"), argumentList);
    }

    inline QDBusPendingReply<> SetUseProxy(const QString &in0, bool in1)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0) << QVariant::fromValue(in1);
        return asyncCallWithArgumentList(QStringLiteral("SetUseProxy"), argumentList);
    }

    inline QDBusPendingReply<bool> GetUseProxy(const QString &in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);
        return asyncCallWithArgumentList(QStringLiteral("GetUseProxy"), argumentList);
    }

    inline QDBusPendingReply<> SetDisableScaling(const QString &in0, bool in1)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0) << QVariant::fromValue(in1);
        return asyncCallWithArgumentList(QStringLiteral("SetDisableScaling"), argumentList);
    }

    inline QDBusPendingReply<bool> GetDisableScaling(const QString &in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);
        return asyncCallWithArgumentList(QStringLiteral("GetDisableScaling"), argumentList);
    }

   Q_SIGNALS: // SIGNALS
       void ItemChanged(const QString &in0, ItemInfo in1, qlonglong in2);
       void NewAppLaunched(const QString &in0);
       void NewAppMarkedAsLaunched(const QString &in0);
       void RemoveFromDesktopFailed(const QString &in0, const QString &in1);
       void RemoveFromDesktopSuccess(const QString &in0);
       void SearchDone(const QStringList &in0);
       void SendToDesktopFailed(const QString &in0, const QString &in1);
       void SendToDesktopSuccess(const QString &in0);
       void UninstallFailed(const QString &in0, const QString &in1);
       void UninstallSuccess(const QString &in0);

       void FullscreenChanged();
       void DisplayModeChanged();
};

namespace com {
  namespace deepin {
    namespace dde {
      namespace daemon {
        typedef ::DBusLauncher DBusLauncher;
      }
    }
  }
}
#endif
