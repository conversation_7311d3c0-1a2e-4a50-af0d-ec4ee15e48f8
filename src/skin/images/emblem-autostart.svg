<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 46.2 (44496) - http://www.bohemiancoding.com/sketch -->
    <title>autostart</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <circle id="path-1" cx="12" cy="12" r="9"></circle>
        <filter x="-16.7%" y="-11.1%" width="133.3%" height="133.3%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="1" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="1" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.2 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="autostart">
            <g id="Oval">
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                <circle stroke-opacity="0.1" stroke="#000000" stroke-width="1" cx="12" cy="12" r="9.5"></circle>
            </g>
            <path d="M15,6 L15,16.2198541 L12,13 L7.53050458,13 L15,6 Z M9,18.5381576 L9,14 L11.3339006,14 L13.0647032,15.7746151 L9,18.5381576 Z" id="Combined-Shape" fill="#303030"></path>
        </g>
    </g>
</svg>