QFrame#ContentFrame{
    background-color: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

QPushButton#CloseButton{
    border-image: url(":/images/skin/dialogs/images/dark_close_small_normal.png")
}

QPushButton#CloseButton:hover{
    border-image: url(":/images/skin/dialogs/images/dark_close_small_hover.png")
}


QPushButton#CloseButton:pressed{
    border-image: url(":/images/skin/dialogs/images/dark_close_small_press.png")
}

QPushButton#ActionButton{
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-right: none;
    border-left: none;
    border-bottom: none;
    color: white;
    text-align: center;
}

QPushButton#ActionButton:hover{
    background-color: transparent;
    color: rgba(255, 255, 255, 0.5);
}

QPushButton#ActionButton:pressed{
    background-color: transparent;
    color: #01bdff;
}

QLabel#VLine{
    background-color:rgba(255, 255, 255, 0.3);
}

QLabel#MessageLabel{
    padding-top: 2px;
    padding-bottom: 2px;
    color: white;
}

QLabel#TipMessageLabel{
    padding-top: 2px;
    padding-bottom: 2px;
    color: #ffa530;
}
