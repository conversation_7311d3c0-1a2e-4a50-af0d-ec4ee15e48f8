<RCC>
    <qresource prefix="/widgets">
        <file>images/all.svg</file>
        <file>images/all-dark.svg</file>
        <file>images/settings.svg</file>
        <file>images/settings-dark.svg</file>
        <file>images/power.svg</file>
        <file>images/power-dark.svg</file>
        <file>images/enter_details_normal.svg</file>
        <file>images/enter_details_normal-dark.svg</file>
        <file>images/drag_indicator.svg</file>
        <file>images/computer-symbolic.svg</file>
        <file>images/folder-documents-symbolic.svg</file>
        <file>images/folder-downloads-symbolic.svg</file>
        <file>images/folder-music-symbolic.svg</file>
        <file>images/folder-pictures-symbolic.svg</file>
        <file>images/folder-videos-symbolic.svg</file>
        <file>images/computer-symbolic_dark.svg</file>
        <file>images/folder-documents-symbolic_dark.svg</file>
        <file>images/folder-downloads-symbolic_dark.svg</file>
        <file>images/folder-music-symbolic_dark.svg</file>
        <file>images/folder-pictures-symbolic_dark.svg</file>
        <file>images/folder-videos-symbolic_dark.svg</file>
        <file>images/setting_dark.svg</file>
        <file>images/setting.svg</file>
        <file>images/shutdown.svg</file>
        <file>images/shutdown_dark.svg</file>
        <file>images/page_indicator.svg</file>
        <file>images/page_indicator_active.svg</file>
        <file>images/application-x-desktop.svg</file>
    </qresource>
</RCC>
