/*
 * Copyright (C) 2017 ~ 2018 Deepin Technology Co., Ltd.
 *
 * Author:     rekols <<EMAIL>>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

#ifndef MINIFRAMESWITCHBTN_H
#define MINIFRAMESWITCHBTN_H

#include "roundedbutton.h"
#include <QWidget>
#include <QLabel>

#include <DGuiApplicationHelper>

DGUI_USE_NAMESPACE

class MiniFrameSwitchBtn : public QWidget
{
    Q_OBJECT

public:
    explicit MiniFrameSwitchBtn(QWidget *parent = nullptr);

    void updateStatus(int status);
    void click();

signals:
    void clicked();

protected:
    void paintEvent(QPaintEvent *event) override;
    void enterEvent(QEvent *event) override;
    void leaveEvent(QEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;

public slots:
    void updateIcon();

private:
    QLabel *m_textLabel;
    QLabel *m_enterIcon;
    QLabel *m_allIconLabel;
    bool m_hover = false;
    QColor m_color;
};

#endif
