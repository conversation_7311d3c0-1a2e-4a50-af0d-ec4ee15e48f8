/*
 * Copyright (C) 2015 ~ 2018 Deepin Technology Co., Ltd.
 *
 * Author:     sbw <<EMAIL>>
 *
 * Maintainer: sbw <<EMAIL>>
 *             kirigaya <<EMAIL>>
 *             Hualet <<EMAIL>>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

#include "util.h"
#include "appsmanager.h"
#include "iconcachemanager.h"

#include <DHiDPIHelper>
#include <DGuiApplicationHelper>
#include <DConfig>

#include <QStandardPaths>
#include <QDir>
#include <QPixmap>
#include <QPainter>
#include <QSvgRenderer>
#include <QImageReader>
#include <QApplication>
#include <QIcon>
#include <QScopedPointer>
#include <QIconEngine>
#include <QSharedPointer>

#include <private/qguiapplication_p.h>
#include <private/qiconloader_p.h>
#include <qpa/qplatformtheme.h>

DWIDGET_USE_NAMESPACE

const QPixmap loadSvg(const QString &fileName, const int size)
{
    if (!QFileInfo::exists(fileName))
        return QPixmap();

    QPixmap pixmap(size, size);
    QSvgRenderer renderer(fileName);
    pixmap.fill(Qt::transparent);

    QPainter painter;
    painter.begin(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing, true);
    renderer.render(&painter);
    painter.end();

    pixmap.setDevicePixelRatio(qRound(qApp->devicePixelRatio()));

    return pixmap;
}

/**
 * @brief renderSVG 根据实体屏幕渲染指定路径、指定大小的图片
 * @param path 渲染图片的路径
 * @param size 渲染图片的大小
 * @return 返回渲染后的pixmap
 */
const QPixmap renderSVG(const QString &path, const QSize &size)
{
    if (!QFileInfo::exists(path))
        return QPixmap();

    QImageReader reader;
    QPixmap pixmap;
    reader.setFileName(path);
    if (reader.canRead()) {
        const qreal ratio = qRound(qApp->devicePixelRatio());
        reader.setScaledSize(size * ratio);
        pixmap = QPixmap::fromImage(reader.read());
        pixmap.setDevicePixelRatio(ratio);
    }
    else {
        pixmap.load(path);
    }

    return pixmap;
}

const QPixmap loadSvg(const QString &fileName, const QSize &size)
{
    if (!QFileInfo::exists(fileName))
         return QPixmap();

    QPixmap pixmap(size);
    QSvgRenderer renderer(fileName);
    pixmap.fill(Qt::transparent);

    QPainter painter;
    painter.begin(&pixmap);
    renderer.render(&painter);
    painter.end();

    return pixmap;
}

const QPixmap loadIco(const QString &fileName, const int size)
{
    QPixmap pixmap;

    QImageReader reader(fileName);

    if (reader.imageCount() > 0) {
        for (int i = 0; i < reader.imageCount(); i++) {
            // 如果ico文件中有多个图标,获取更大一点的图标
            if (reader.jumpToImage(i)) {
                QImage image = reader.read();
                if (image.size().width() >= size ) {
                    pixmap = QPixmap::fromImage(image);
                    break;
                }
            }
        }

        // 如果列表中没有合适的尺寸,则用列表中最大的尺寸替代
        if (pixmap.isNull() && reader.jumpToImage(reader.imageCount()  - 1)) {
            QImage image = reader.read();
            pixmap = QPixmap::fromImage(image);
        }
    }

    if (pixmap.isNull())
        pixmap.load(fileName);

    return pixmap;
}

/**
 * @brief SettingsPtr 根据给定信息返回一个QGSettings指针
 * @param schema_id The id of the schema
 * @param path If non-empty, specifies the path for a relocatable schema
 * @param parent 创建指针的父对象
 * @return 返回QGSetting指针对象
 */
QGSettings *SettingsPtr(const QString &schema_id, const QByteArray &path, QObject *parent)
{
    if (QGSettings::isSchemaInstalled(schema_id.toUtf8())) {
        QGSettings *settings = new QGSettings(schema_id.toUtf8(), path, parent);
        return settings;
    }
    qDebug() << "Cannot find gsettings, schema_id:" << schema_id;
    return nullptr;
}

/**
 * @brief SettingsPtr 根据给定信息返回一个QGSettings指针
 * @param module 传入QGSettings构造函数时，会添加"com.deepin.dde.dock.module."前缀
 * @param path If non-empty, specifies the path for a relocatable schema
 * @param parent 创建指针的付对象
 * @return
 */
QGSettings *ModuleSettingsPtr(const QString &module, const QByteArray &path, QObject *parent)
{
    return SettingsPtr("com.deepin.dde.dock.module." + module, path, parent);
}

/* convert 'some-key' to 'someKey' or 'SomeKey'.
 * the second form is needed for appending to 'set' for 'setSomeKey'
 */
QString qtify_name(const char *name)
{
    bool next_cap = false;
    QString result;

    while (*name) {
        if (*name == '-') {
            next_cap = true;
        } else if (next_cap) {
            result.append(QChar(*name).toUpper().toLatin1());
            next_cap = false;
        } else {
            result.append(*name);
        }

        name++;
    }

    return result;
}

/**
 * @brief SettingValue 根据给定信息返回获取的值
 * @param schema_id The id of the schema
 * @param path If non-empty, specifies the path for a relocatable schema
 * @param key 对应信息的key值
 * @param fallback 如果找不到信息，返回此默认值
 * @return
 */
QVariant SettingValue(const QString &schema_id, const QByteArray &path, const QString &key, const QVariant &fallback)
{
    const QGSettings *settings = SettingsPtr(schema_id, path);

    if (settings && ((settings->keys().contains(key)) || settings->keys().contains(qtify_name(key.toUtf8().data())))) {
        QVariant v = settings->get(key);
        delete settings;
        return v;
    } else {
        qDebug() << "Cannot find gsettings, schema_id:" << schema_id
                 << " path:" << path << " key:" << key
                 << "Use fallback value:" << fallback;
        return fallback;
    }
}

bool createCalendarIcon(const QString &fileName)
{
    static const QByteArrayList dayList= {"<polygon id=\"test-d\" points=\"41.632 58.788 41.632 29.6 37.448 29.6 33.304 32.623 33.304 37.067 37.448 34.024 37.448 58.788\"/>\n"
                                          , "<path id=\"test-d\" d=\"M47.4575195,58.7881788 L47.4575195,54.6040967 L36.3066406,54.6040967 L45.6958008,42.8326124 C46.8702799,41.3378207 47.4575195,39.6294874 47.4575195,37.7076124 C47.4308268,35.3052686 46.6233724,33.323335 45.0351563,31.7618116 C43.4736328,30.1869418 41.4383138,29.3861605 38.9291992,29.3594678 C36.6870117,29.3861605 34.7985026,30.1735954 33.2636719,31.7217725 C31.7421875,33.3099887 30.9280599,35.318615 30.8212891,37.7476514 L34.9853516,37.7476514 C35.1321615,36.413016 35.5992839,35.3786736 36.3867188,34.6446241 C37.1474609,33.9105746 38.1017253,33.5435499 39.2495117,33.5435499 C40.5441081,33.5702426 41.5450846,33.9906527 42.2524414,34.8047803 C42.9331055,35.6189079 43.2734375,36.5731723 43.2734375,37.6675733 C43.2734375,38.0813103 43.2200521,38.52174 43.1132813,38.9888624 C42.953125,39.4826775 42.652832,40.0165316 42.2124023,40.5904249 L30.8212891,54.8443311 L30.8212891,58.7881788 L47.4575195,58.7881788 Z\"/>\n"
                                          , "<path id=\"test-d\" d=\"M38.3085938,59.0284131 C40.8844401,59.0017204 42.9931641,58.1809196 44.6347656,56.5660108 C46.3030599,54.991141 47.1505534,52.8357048 47.1772461,50.0997022 C47.1772461,48.8718376 46.9169922,47.6973585 46.3964844,46.5762647 C45.8492839,45.455171 44.9617513,44.4942335 43.7338867,43.6934522 C44.9350586,42.8793246 45.7692057,41.951753 46.2363281,40.9107374 C46.6367188,39.8697217 46.8369141,38.788667 46.8369141,37.6675733 C46.8102214,35.4520785 46.0494792,33.5302035 44.5546875,31.9019483 C42.9931641,30.233654 40.8844401,29.3861605 38.2285156,29.3594678 C36.133138,29.3861605 34.3313802,30.1335564 32.8232422,31.6016553 C31.2884115,33.0831006 30.4142253,34.904878 30.2006836,37.0669874 L34.3847656,37.0669874 C34.6516927,35.8658155 35.1722005,34.9782829 35.9462891,34.4043897 C36.6803385,33.8304965 37.4944661,33.5435499 38.3886719,33.5435499 C39.6298828,33.5702426 40.6442057,33.96396 41.4316406,34.7247022 C42.2190755,35.5121371 42.6261393,36.5197868 42.652832,37.7476514 C42.652832,38.9621696 42.2591146,39.9497999 41.4716797,40.710542 C40.6842448,41.4979769 39.5764974,41.8916944 38.1484375,41.8916944 L36.7070313,41.8916944 L36.7070313,45.5953077 L38.46875,45.5953077 C39.7633464,45.5953077 40.8310547,45.9890251 41.671875,46.77646 C42.5260417,47.5905876 42.9664714,48.7383741 42.9931641,50.2198194 C42.9664714,51.674572 42.5260417,52.7956657 41.671875,53.5831006 C40.8310547,54.423921 39.7967122,54.8443311 38.5688477,54.8443311 C37.4210612,54.8443311 36.5001628,54.5306918 35.8061523,53.9034131 C35.1254883,53.3028272 34.5983073,52.5153923 34.2246094,51.5411085 L30.0405273,51.5411085 C30.4943034,53.9701449 31.4886068,55.8252881 33.0234375,57.1065381 C34.5582682,58.3877881 36.319987,59.0284131 38.3085938,59.0284131 Z\"/>\n"
                                          , "<polygon id=\"test-d\" points=\"46.356 58.788 46.356 54.444 48.679 54.444 48.679 50.5 46.356 50.5 46.356 42.112 42.172 42.112 42.172 50.5 34.545 50.5 44.415 29.6 39.75 29.6 30 50.5 30 54.444 42.172 54.444 42.172 58.788\"/>\n"
                                          , "<path id=\"test-d\" d=\"M39.5498047,59.0284131 C41.0179036,59.0017204 42.2858073,58.6613884 43.3535156,58.007417 C44.4479167,57.3801384 45.2820638,56.686128 45.855957,55.9253858 C46.5099284,55.1512973 46.9770508,54.2637647 47.2573242,53.2627881 C47.5242513,52.2484652 47.6577148,50.7937126 47.6577148,48.8985303 C47.6577148,47.6706657 47.6176758,46.6363233 47.5375977,45.795503 C47.4575195,44.968029 47.324056,44.2673454 47.137207,43.6934522 C46.7768555,42.6390902 46.1896159,41.7115186 45.3754883,40.9107374 C44.8282878,40.3101514 44.1142578,39.80299 43.2333984,39.389253 C42.2991536,39.0155551 41.2714844,38.8153598 40.1503906,38.788667 C38.4020182,38.788667 36.847168,39.3225212 35.4858398,40.3902295 L35.4858398,33.7837842 L47.097168,33.7837842 L47.097168,29.5997022 L31.5419922,29.5997022 L31.5419922,45.3750928 L35.4858398,45.3750928 C35.9796549,44.4808871 36.5402018,43.8536085 37.1674805,43.4932569 C37.7947591,43.1462517 38.4887695,42.9727491 39.2495117,42.9727491 C40.1036784,42.9727491 40.8177083,43.0928663 41.3916016,43.3331006 C41.9654948,43.6267204 42.3992513,44.0204379 42.6928711,44.514253 C43.2400716,45.5552686 43.5003255,46.9099236 43.4736328,48.5782178 C43.4736328,49.2588819 43.4602865,49.9595655 43.4335938,50.6802686 C43.3802083,51.4143181 43.2333984,52.0949821 42.9931641,52.7222608 C42.766276,53.3495394 42.3658854,53.8567009 41.7919922,54.2437452 C41.1914063,54.6441358 40.390625,54.8443311 39.3896484,54.8443311 C37.0540365,54.8176384 35.6593424,53.6831983 35.2055664,51.4410108 L31.0214844,51.4410108 C31.421875,54.123628 32.4562174,56.0655225 34.1245117,57.2666944 C35.7661133,58.4411736 37.5745443,59.0284131 39.5498047,59.0284131 Z\"/>\n"
                                          , "<path id=\"test-d\" d=\"M36.8271484,58.7681592 C37.7613932,59.0350863 38.8357747,59.1151644 40.050293,59.0083936 C41.2114258,58.9149691 42.2124023,58.6680616 43.0532227,58.267671 C43.894043,57.8672803 44.6080729,57.3901482 45.1953125,56.8362745 C45.7825521,56.2824008 46.2563477,55.6951612 46.6166992,55.0745557 C46.9770508,54.4539502 47.2539876,53.8733838 47.4475098,53.3328565 C47.6410319,52.7923292 47.7744954,52.3252068 47.8479004,51.9314893 C47.9213053,51.5377719 47.964681,51.2942009 47.9780273,51.2007764 C47.9913737,51.0139275 48.0013835,50.7870394 48.0080566,50.5201124 C48.0147298,50.2531853 48.0180664,49.9895948 48.0180664,49.7293409 L48.0180664,49.7293409 L48.0180664,48.5782178 C48.0180664,48.3246371 47.9813639,47.9742953 47.907959,47.5271924 C47.834554,47.0800896 47.7044271,46.5996208 47.5175781,46.0857862 C47.3307292,45.5719515 47.0738118,45.0447706 46.7468262,44.5042432 C46.4198405,43.9637159 46.0094401,43.4732374 45.515625,43.0328077 C45.0218099,42.592378 44.4312337,42.2253532 43.7438965,41.9317335 C43.0565592,41.6381137 42.2524414,41.4779574 41.331543,41.4512647 C40.690918,41.424572 40.1270345,41.4345818 39.6398926,41.481294 C39.1527507,41.5280062 38.7156576,41.6648064 38.3286133,41.8916944 C38.5154622,41.5713819 38.7490234,41.1442986 39.0292969,40.6104444 C39.3095703,40.0765902 39.619873,39.4826775 39.9602051,38.8287061 C40.3005371,38.1747348 40.6542155,37.4873975 41.0212402,36.7666944 C41.388265,36.0459913 41.7486165,35.3319613 42.1022949,34.6246045 L42.1022949,34.6246045 L42.6203003,33.5885938 C42.788798,33.2515984 42.953125,32.9229444 43.1132813,32.6026319 C43.4335938,31.9620069 43.7138672,31.3947868 43.9541016,30.9009717 C44.1943359,30.4071566 44.3845215,30.0167758 44.5246582,29.7298292 C44.6647949,29.4428825 44.7281901,29.2994092 44.7148438,29.2994092 L44.7148438,29.2994092 L39.9902344,29.2994092 C38.695638,31.9019483 37.527832,34.2575798 36.4868164,36.3663038 C36.0463867,37.2738558 35.605957,38.1680616 35.1655273,39.048921 C34.7250977,39.9297803 34.3280436,40.7372348 33.9743652,41.4712842 C33.6206868,42.2053337 33.3237305,42.8226026 33.0834961,43.3230909 C32.8432617,43.8235792 32.6964518,44.1405551 32.6430664,44.2740186 C32.4295247,44.8078728 32.2426758,45.3016879 32.0825195,45.7554639 C31.9223633,46.20924 31.77889,46.6696892 31.6520996,47.1368116 C31.5253092,47.603934 31.4152018,48.0944125 31.3217773,48.6082471 C31.2283529,49.1220818 31.1416016,49.7059848 31.0615234,50.3599561 C31.008138,50.8003858 31.024821,51.33424 31.1115723,51.9615186 C31.1983236,52.5887973 31.3718262,53.2427686 31.6320801,53.9234327 C31.892334,54.6040967 32.249349,55.2680779 32.703125,55.915376 C33.156901,56.5626742 33.7241211,57.1332308 34.4047852,57.627046 C35.0854492,58.1208611 35.8929036,58.5012321 36.8271484,58.7681592 Z M39.8825783,54.7244182 L39.6298828,54.7242139 C38.882487,54.7242139 38.2585449,54.6307894 37.7580566,54.4439405 C37.2575684,54.2570915 36.847168,54.0168571 36.5268555,53.7232374 C36.206543,53.4296176 35.9596354,53.0926221 35.7861328,52.712251 C35.6126302,52.3318799 35.4891764,51.9381625 35.4157715,51.5310987 C35.3423665,51.1240349 35.2989909,50.7169711 35.2856445,50.3099073 C35.2722982,49.9028435 35.265625,49.5324821 35.265625,49.1988233 C35.265625,49.0520134 35.2989909,48.8418083 35.3657227,48.5682081 C35.4324544,48.2946078 35.5425618,47.9976514 35.6960449,47.6773389 C35.849528,47.3570264 36.0430501,47.0333773 36.2766113,46.7063917 C36.5101725,46.379406 36.7871094,46.0891228 37.1074219,45.835542 C37.4277344,45.5819613 37.7947591,45.381766 38.2084961,45.2349561 C38.6222331,45.0881462 39.0826823,45.0347608 39.5898438,45.0747999 C40.6708984,45.154878 41.5050456,45.3951124 42.0922852,45.795503 C42.6795247,46.1958936 43.1032715,46.6730258 43.3635254,47.2268995 C43.6237793,47.7807732 43.7739258,48.3646762 43.8139648,48.9786085 C43.8540039,49.5925407 43.8740234,50.1530876 43.8740234,50.6602491 C43.8740234,50.7803663 43.8540039,50.9638786 43.8139648,51.2107862 C43.7739258,51.4576937 43.6971842,51.7346306 43.5837402,52.0415967 C43.4702962,52.3485629 43.3134766,52.6622022 43.1132813,52.9825147 C42.9130859,53.3028272 42.6561686,53.5931104 42.3425293,53.8533643 C42.02889,54.1136182 41.6485189,54.3271599 41.201416,54.4939893 C40.7543132,54.6608187 40.2304688,54.7375603 39.6298828,54.7242139 L39.8825783,54.7244182 Z\"/>\n"
                                          , "<polygon id=\"test-d\" points=\"38.309 58.788 48.198 33.784 48.198 29.6 31.582 29.6 31.582 38.008 35.766 38.008 35.766 33.784 43.514 33.784 33.644 58.788\"/>\n"
                                          , "<path id=\"test-d\" d=\"M39.3295898,59.0284131 C41.8253581,59.0017204 43.934082,58.1809196 45.6557617,56.5660108 C47.3507487,54.991141 48.2115885,52.8890902 48.2382813,50.2598585 C48.2115885,47.4437777 47.0638021,45.2549756 44.7949219,43.6934522 C45.6624349,42.93271 46.3697917,42.0585238 46.9169922,41.0708936 C47.4375,40.1499952 47.6977539,39.0555941 47.6977539,37.7876905 C47.6710612,35.358654 46.8769531,33.3500277 45.3154297,31.7618116 C43.7672526,30.1869418 41.7719727,29.3861605 39.3295898,29.3594678 C36.9272461,29.3861605 34.965332,30.1869418 33.4438477,31.7618116 C31.8289388,33.3500277 31.008138,35.358654 30.9814453,37.7876905 C30.9814453,39.0555941 31.2683919,40.1499952 31.8422852,41.0708936 C32.3361003,42.0585238 33.0167643,42.93271 33.8842773,43.6934522 C31.6153971,45.2549756 30.4676107,47.4437777 30.440918,50.2598585 C30.4676107,52.8890902 31.3551432,54.991141 33.1035156,56.5660108 C34.7718099,58.1809196 36.847168,59.0017204 39.3295898,59.0284131 Z M39.3295898,41.8916944 C38.2485352,41.8916944 37.2942708,41.5246696 36.4667969,40.7906202 C35.6259766,40.0832634 35.1922201,39.0555941 35.1655273,37.7076124 C35.1922201,36.3195915 35.6259766,35.2719027 36.4667969,34.564546 C37.2942708,33.8838819 38.2485352,33.5435499 39.3295898,33.5435499 C40.4506836,33.5435499 41.4249674,33.8838819 42.2524414,34.564546 C43.066569,35.2719027 43.4869792,36.3195915 43.5136719,37.7076124 C43.4869792,39.0555941 43.066569,40.0832634 42.2524414,40.7906202 C41.4249674,41.5246696 40.4506836,41.8916944 39.3295898,41.8916944 Z M39.3295898,54.8443311 C38.0483398,54.8443311 36.960612,54.3972282 36.0664063,53.5030225 C35.1321615,52.6488558 34.6516927,51.5544548 34.625,50.2198194 C34.6516927,48.8584913 35.1321615,47.7507439 36.0664063,46.8965772 C36.960612,46.0557569 38.0483398,45.6220004 39.3295898,45.5953077 C40.6508789,45.6220004 41.7586263,46.0557569 42.652832,46.8965772 C43.5603841,47.7507439 44.0275065,48.8584913 44.0541992,50.2198194 C44.0275065,51.5544548 43.5603841,52.6488558 42.652832,53.5030225 C41.7586263,54.3972282 40.6508789,54.8443311 39.3295898,54.8443311 Z\"/>\n"
                                          , "<path id=\"test-d\" d=\"M38.6088867,58.7881788 C39.8767904,56.2256788 41.0245768,53.9034131 42.0522461,51.8213819 C42.4926758,50.9405225 42.9230957,50.0629997 43.3435059,49.1888135 C43.763916,48.3146273 44.1509603,47.5205193 44.5046387,46.8064893 C44.8583171,46.0924594 45.1519368,45.4885368 45.385498,44.9947217 C45.6190592,44.5009066 45.7625326,44.1805941 45.815918,44.0337842 C46.0294596,43.4999301 46.2163086,43.006115 46.3764648,42.5523389 C46.5366211,42.0985629 46.6800944,41.6381137 46.8068848,41.1709913 C46.9336751,40.7038689 47.0437826,40.2133904 47.137207,39.6995557 C47.2306315,39.1857211 47.3173828,38.6018181 47.3974609,37.9478467 C47.4508464,37.507417 47.4375,36.9768995 47.3574219,36.356294 C47.2773438,35.7356885 47.1138509,35.0950635 46.8669434,34.434419 C46.6200358,33.7737745 46.2763672,33.1231397 45.8359375,32.4825147 C45.3955078,31.8418897 44.8416341,31.2813428 44.1743164,30.8008741 C43.5069987,30.3204053 42.709554,29.9533806 41.7819824,29.6997999 C40.8544108,29.4462191 39.7833659,29.3728142 38.5688477,29.479585 C37.4077148,29.5863558 36.4034017,29.8332634 35.5559082,30.2203077 C34.7084147,30.6073519 33.9943848,31.0678012 33.4138184,31.6016553 C32.833252,32.1355095 32.362793,32.7060661 32.0024414,33.3133252 C31.6420898,33.9205844 31.3618164,34.491141 31.1616211,35.0249952 C30.9614258,35.5588493 30.8246257,36.015962 30.7512207,36.3963331 C30.6778158,36.7767042 30.6344401,37.0136019 30.6210938,37.1070264 C30.594401,37.2938754 30.5777181,37.5207634 30.5710449,37.7876905 C30.5643717,38.0546176 30.5610352,38.3182081 30.5610352,38.578462 C30.5610352,38.8387159 30.5643717,39.0756137 30.5710449,39.2891553 C30.5777181,39.502697 30.5810547,39.6495069 30.5810547,39.729585 C30.5810547,39.9831657 30.6244303,40.3335075 30.7111816,40.7806104 C30.7979329,41.2277133 30.9380697,41.708182 31.1315918,42.2220167 C31.3251139,42.7358513 31.5820313,43.2630323 31.9023438,43.8035596 C32.2226563,44.344087 32.6230469,44.8345655 33.1035156,45.2749952 C33.5839844,45.7154249 34.154541,46.0824496 34.8151855,46.3760694 C35.4758301,46.6696892 36.246582,46.8298454 37.1274414,46.8565381 C37.7680664,46.8832308 38.3319499,46.8732211 38.8190918,46.8265088 C39.3062337,46.7797966 39.7433268,46.6429965 40.1303711,46.4161085 C39.8767904,46.8565381 39.5464681,47.4637973 39.1394043,48.2378858 C38.7323405,49.0119743 38.2952474,49.8594678 37.828125,50.7803663 C37.3610026,51.7012647 36.887207,52.6355095 36.4067383,53.5831006 C35.9262695,54.5306918 35.492513,55.3915316 35.1054688,56.1656202 C34.7184245,56.9397087 34.4047852,57.5703239 34.1645508,58.0574659 C33.9243164,58.5446078 33.8108724,58.7881788 33.8242188,58.7881788 L33.8242188,58.7881788 L38.6088867,58.7881788 Z M39.1188287,43.2389347 L38.8691406,43.233003 C37.8548177,43.1796176 37.0673828,42.9560661 36.5068359,42.5623487 C35.9462891,42.1686312 35.5358887,41.6948357 35.2756348,41.140962 C35.0153809,40.5870883 34.8585612,39.9965121 34.8051758,39.3692335 C34.7517904,38.7419548 34.7250977,38.1680616 34.7250977,37.6475538 C34.7250977,37.4873975 34.7851563,37.1937777 34.9052734,36.7666944 C35.0253906,36.3396111 35.2389323,35.8991814 35.5458984,35.4454053 C35.8528646,34.9916293 36.2699382,34.5778923 36.7971191,34.2041944 C37.3243001,33.8304965 38.0016276,33.623628 38.8291016,33.5835889 C39.5764974,33.5435499 40.2004395,33.606945 40.7009277,33.7737745 C41.201416,33.9406039 41.6118164,34.1708285 41.9321289,34.4644483 C42.2524414,34.7580681 42.499349,35.1017367 42.6728516,35.4954542 C42.8463542,35.8891716 42.9698079,36.299572 43.0432129,36.7266553 C43.1166178,37.1537387 43.1566569,37.5741488 43.1633301,37.9878858 C43.1700033,38.4016228 43.180013,38.7753207 43.1933594,39.1089795 C43.1933594,39.2691358 43.1599935,39.4826775 43.0932617,39.7496045 C43.0265299,40.0165316 42.9164225,40.3068148 42.7629395,40.6204542 C42.6094564,40.9340935 42.4159342,41.2510694 42.182373,41.5713819 C41.9488118,41.8916944 41.671875,42.178641 41.3515625,42.4322217 C41.03125,42.6858025 40.6642253,42.8893344 40.2504883,43.0428174 C39.8367513,43.1963005 39.3763021,43.2596957 38.8691406,43.233003 L39.1188287,43.2389347 Z\"/>\n"
                                          , "<path id=\"test-d\" d=\"M31.1318359,57.3125 L31.1318359,28.1240234 L26.9477539,28.1240234 L22.8037109,31.1469727 L22.8037109,35.5913086 L26.9477539,32.5483398 L26.9477539,57.3125 L31.1318359,57.3125 Z M49.8087891,57.5527344 C52.0776693,57.5260417 54.0062174,56.7919922 55.5944336,55.3505859 C57.2093424,53.8958333 58.0434896,51.8271484 58.096875,49.1445313 L58.096875,36.2719727 C58.0434896,33.5760091 57.2093424,31.500651 55.5944336,30.0458984 C54.0062174,28.6044922 52.0776693,27.8837891 49.8087891,27.8837891 C47.6066406,27.8837891 45.7114583,28.6044922 44.1232422,30.0458984 C42.4282552,31.500651 41.5674154,33.5760091 41.5407227,36.2719727 L41.5407227,49.1445313 C41.5674154,51.8271484 42.4282552,53.8958333 44.1232422,55.3505859 C45.7114583,56.7919922 47.6066406,57.5260417 49.8087891,57.5527344 Z M49.8087891,53.3686523 C47.1395182,53.3419596 45.7781901,51.8538411 45.7248047,48.9042969 L45.7248047,36.5322266 C45.7781901,33.609375 47.1395182,32.1212565 49.8087891,32.0678711 C52.4647135,32.1212565 53.8327148,33.609375 53.912793,36.5322266 L53.912793,48.9042969 C53.8327148,51.8538411 52.4647135,53.3419596 49.8087891,53.3686523 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M31.1318359,57.3125 L31.1318359,28.1240234 L26.9477539,28.1240234 L22.8037109,31.1469727 L22.8037109,35.5913086 L26.9477539,32.5483398 L26.9477539,57.3125 L31.1318359,57.3125 Z M52.1110352,57.3125 L52.1110352,28.1240234 L47.9269531,28.1240234 L43.7829102,31.1469727 L43.7829102,35.5913086 L47.9269531,32.5483398 L47.9269531,57.3125 L52.1110352,57.3125 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M31.1318359,57.3125 L31.1318359,28.1240234 L26.9477539,28.1240234 L22.8037109,31.1469727 L22.8037109,35.5913086 L26.9477539,32.5483398 L26.9477539,57.3125 L31.1318359,57.3125 Z M57.9367187,57.3125 L57.9367187,53.128418 L46.7858398,53.128418 L56.175,41.3569336 C57.3494792,39.8621419 57.9367187,38.1538086 57.9367187,36.2319336 C57.910026,33.8295898 57.1025716,31.8476563 55.5143555,30.2861328 C53.952832,28.711263 51.917513,27.9104818 49.4083984,27.8837891 C47.1662109,27.9104818 45.2777018,28.6979167 43.7428711,30.2460938 C42.2213867,31.8343099 41.4072591,33.8429362 41.3004883,36.2719727 L45.4645508,36.2719727 C45.6113607,34.9373372 46.0784831,33.9029948 46.865918,33.1689453 C47.6266602,32.4348958 48.5809245,32.0678711 49.7287109,32.0678711 C51.0233073,32.0945638 52.0242839,32.514974 52.7316406,33.3291016 C53.4123047,34.1432292 53.7526367,35.0974935 53.7526367,36.1918945 C53.7526367,36.6056315 53.6992513,37.0460612 53.5924805,37.5131836 C53.4323242,38.0069987 53.1320312,38.5408529 52.6916016,39.1147461 L41.3004883,53.3686523 L41.3004883,57.3125 L57.9367187,57.3125 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M31.1318359,57.3125 L31.1318359,28.1240234 L26.9477539,28.1240234 L22.8037109,31.1469727 L22.8037109,35.5913086 L26.9477539,32.5483398 L26.9477539,57.3125 L31.1318359,57.3125 Z M48.787793,57.5527344 C51.3636393,57.5260417 53.4723633,56.7052409 55.1139648,55.090332 C56.7822591,53.5154622 57.6297526,51.360026 57.6564453,48.6240234 C57.6564453,47.3961589 57.3961914,46.2216797 56.8756836,45.1005859 C56.3284831,43.9794922 55.4409505,43.0185547 54.2130859,42.2177734 C55.4142578,41.4036458 56.2484049,40.4760742 56.7155273,39.4350586 C57.115918,38.394043 57.3161133,37.3129883 57.3161133,36.1918945 C57.2894206,33.9763997 56.5286784,32.0545247 55.0338867,30.4262695 C53.4723633,28.7579753 51.3636393,27.9104818 48.7077148,27.8837891 C46.6123372,27.9104818 44.8105794,28.6578776 43.3024414,30.1259766 C41.7676107,31.6074219 40.8934245,33.4291992 40.6798828,35.5913086 L44.8639648,35.5913086 C45.1308919,34.3901367 45.6513997,33.5026042 46.4254883,32.9287109 C47.1595378,32.3548177 47.9736654,32.0678711 48.8678711,32.0678711 C50.109082,32.0945638 51.1234049,32.4882813 51.9108398,33.2490234 C52.6982747,34.0364583 53.1053385,35.0441081 53.1320312,36.2719727 C53.1320312,37.4864909 52.7383138,38.4741211 51.9508789,39.2348633 C51.163444,40.0222982 50.0556966,40.4160156 48.6276367,40.4160156 L47.1862305,40.4160156 L47.1862305,44.1196289 L48.9479492,44.1196289 C50.2425456,44.1196289 51.3102539,44.5133464 52.1510742,45.3007813 C53.0052409,46.1149089 53.4456706,47.2626953 53.4723633,48.7441406 C53.4456706,50.1988932 53.0052409,51.319987 52.1510742,52.1074219 C51.3102539,52.9482422 50.2759115,53.3686523 49.0480469,53.3686523 C47.9002604,53.3686523 46.979362,53.055013 46.2853516,52.4277344 C45.6046875,51.8271484 45.0775065,51.0397135 44.7038086,50.0654297 L40.5197266,50.0654297 C40.9735026,52.4944661 41.967806,54.3496094 43.5026367,55.6308594 C45.0374674,56.9121094 46.7991862,57.5527344 48.787793,57.5527344 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M31.1318359,57.3125 L31.1318359,28.1240234 L26.9477539,28.1240234 L22.8037109,31.1469727 L22.8037109,35.5913086 L26.9477539,32.5483398 L26.9477539,57.3125 L31.1318359,57.3125 Z M56.8356445,57.3125 L56.8356445,52.9682617 L59.1579102,52.9682617 L59.1579102,49.0244141 L56.8356445,49.0244141 L56.8356445,40.6362305 L52.6515625,40.6362305 L52.6515625,49.0244141 L45.0241211,49.0244141 L54.89375,28.1240234 L50.2291992,28.1240234 L40.4796875,49.0244141 L40.4796875,52.9682617 L52.6515625,52.9682617 L52.6515625,57.3125 L56.8356445,57.3125 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M31.1318359,57.3125 L31.1318359,28.1240234 L26.9477539,28.1240234 L22.8037109,31.1469727 L22.8037109,35.5913086 L26.9477539,32.5483398 L26.9477539,57.3125 L31.1318359,57.3125 Z M50.0290039,57.5527344 C51.4971029,57.5260417 52.7650065,57.1857096 53.8327148,56.5317383 C54.9271159,55.9044596 55.761263,55.2104492 56.3351562,54.449707 C56.9891276,53.6756185 57.45625,52.7880859 57.7365234,51.7871094 C58.0034505,50.7727865 58.1369141,49.3180339 58.1369141,47.4228516 C58.1369141,46.194987 58.096875,45.1606445 58.0167969,44.3198242 C57.9367187,43.4923503 57.8032552,42.7916667 57.6164062,42.2177734 C57.2560547,41.1634115 56.6688151,40.2358398 55.8546875,39.4350586 C55.307487,38.8344727 54.593457,38.3273112 53.7125977,37.9135742 C52.7783529,37.5398763 51.7506836,37.339681 50.6295898,37.3129883 C48.8812174,37.3129883 47.3263672,37.8468424 45.9650391,38.9145508 L45.9650391,32.3081055 L57.5763672,32.3081055 L57.5763672,28.1240234 L42.0211914,28.1240234 L42.0211914,43.8994141 L45.9650391,43.8994141 C46.4588542,43.0052083 47.019401,42.3779297 47.6466797,42.0175781 C48.2739583,41.6705729 48.9679687,41.4970703 49.7287109,41.4970703 C50.5828776,41.4970703 51.2969076,41.6171875 51.8708008,41.8574219 C52.444694,42.1510417 52.8784505,42.5447591 53.1720703,43.0385742 C53.7192708,44.0795898 53.9795247,45.4342448 53.952832,47.1025391 C53.952832,47.7832031 53.9394857,48.4838867 53.912793,49.2045898 C53.8594076,49.9386393 53.7125977,50.6193034 53.4723633,51.246582 C53.2454753,51.8738607 52.8450846,52.3810221 52.2711914,52.7680664 C51.6706055,53.168457 50.8698242,53.3686523 49.8688477,53.3686523 C47.5332357,53.3419596 46.1385417,52.2075195 45.6847656,49.965332 L41.5006836,49.965332 C41.9010742,52.6479492 42.9354167,54.5898438 44.6037109,55.7910156 C46.2453125,56.9654948 48.0537435,57.5527344 50.0290039,57.5527344 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M31.1318359,57.3125 L31.1318359,28.1240234 L26.9477539,28.1240234 L22.8037109,31.1469727 L22.8037109,35.5913086 L26.9477539,32.5483398 L26.9477539,57.3125 L31.1318359,57.3125 Z M47.3063477,57.2924805 C48.2405924,57.5594076 49.314974,57.6394857 50.5294922,57.5327148 C51.690625,57.4392904 52.6916016,57.1923828 53.5324219,56.7919922 C54.3732422,56.3916016 55.0872721,55.9144694 55.6745117,55.3605957 C56.2617513,54.806722 56.7355469,54.2194824 57.0958984,53.598877 C57.45625,52.9782715 57.7331868,52.3977051 57.926709,51.8571777 C58.1202311,51.3166504 58.2536947,50.849528 58.3270996,50.4558105 C58.4005046,50.0620931 58.4438802,49.8185221 58.4572266,49.7250977 C58.4705729,49.5382487 58.4805827,49.3113607 58.4872559,49.0444336 C58.493929,48.7775065 58.4972656,48.513916 58.4972656,48.2536621 L58.4972656,48.2536621 L58.4972656,47.1025391 C58.4972656,46.8489583 58.4605632,46.4986165 58.3871582,46.0515137 C58.3137533,45.6044108 58.1836263,45.1239421 57.9967773,44.6101074 C57.8099284,44.0962728 57.5530111,43.5690918 57.2260254,43.0285645 C56.8990397,42.4880371 56.4886393,41.9975586 55.9948242,41.5571289 C55.5010091,41.1166992 54.9104329,40.7496745 54.2230957,40.4560547 C53.5357585,40.1624349 52.7316406,40.0022786 51.8107422,39.9755859 C51.1701172,39.9488932 50.6062337,39.958903 50.1190918,40.0056152 C49.6319499,40.0523275 49.1948568,40.1891276 48.8078125,40.4160156 C48.9946615,40.0957031 49.2282227,39.6686198 49.5084961,39.1347656 C49.7887695,38.6009115 50.0990723,38.0069987 50.4394043,37.3530273 C50.7797363,36.699056 51.1334147,36.0117188 51.5004395,35.2910156 C51.8674642,34.5703125 52.2278158,33.8562826 52.5814941,33.1489258 L52.5814941,33.1489258 L53.0994995,32.112915 C53.2679972,31.7759196 53.4323242,31.4472656 53.5924805,31.1269531 C53.912793,30.4863281 54.1930664,29.9191081 54.4333008,29.425293 C54.6735352,28.9314779 54.8637207,28.541097 55.0038574,28.2541504 C55.1439941,27.9672038 55.2073893,27.8237305 55.194043,27.8237305 L55.194043,27.8237305 L50.4694336,27.8237305 C49.1748372,30.4262695 48.0070312,32.781901 46.9660156,34.890625 C46.5255859,35.7981771 46.0851562,36.6923828 45.6447266,37.5732422 C45.2042969,38.4541016 44.8072428,39.261556 44.4535645,39.9956055 C44.0998861,40.7296549 43.8029297,41.3469238 43.5626953,41.8474121 C43.3224609,42.3479004 43.175651,42.6648763 43.1222656,42.7983398 C42.908724,43.332194 42.721875,43.8260091 42.5617187,44.2797852 C42.4015625,44.7335612 42.2580892,45.1940104 42.1312988,45.6611328 C42.0045085,46.1282552 41.894401,46.6187337 41.8009766,47.1325684 C41.7075521,47.646403 41.6208008,48.230306 41.5407227,48.8842773 C41.4873372,49.324707 41.5040202,49.8585612 41.5907715,50.4858398 C41.6775228,51.1131185 41.8510254,51.7670898 42.1112793,52.4477539 C42.3715332,53.128418 42.7285482,53.7923991 43.1823242,54.4396973 C43.6361003,55.0869954 44.2033203,55.6575521 44.8839844,56.1513672 C45.5646484,56.6451823 46.3721029,57.0255534 47.3063477,57.2924805 Z M50.3617775,53.2487394 L50.109082,53.2485352 C49.3616862,53.2485352 48.7377441,53.1551107 48.2372559,52.9682617 C47.7367676,52.7814128 47.3263672,52.5411784 47.0060547,52.2475586 C46.6857422,51.9539388 46.4388346,51.6169434 46.265332,51.2365723 C46.0918294,50.8562012 45.9683757,50.4624837 45.8949707,50.0554199 C45.8215658,49.6483561 45.7781901,49.2412923 45.7648437,48.8342285 C45.7514974,48.4271647 45.7448242,48.0568034 45.7448242,47.7231445 C45.7448242,47.5763346 45.7781901,47.3661296 45.8449219,47.0925293 C45.9116536,46.818929 46.0217611,46.5219727 46.1752441,46.2016602 C46.3287272,45.8813477 46.5222493,45.5576986 46.7558105,45.2307129 C46.9893717,44.9037272 47.2663086,44.613444 47.5866211,44.3598633 C47.9069336,44.1062826 48.2739583,43.9060872 48.6876953,43.7592773 C49.1014323,43.6124674 49.5618815,43.559082 50.069043,43.5991211 C51.1500977,43.6791992 51.9842448,43.9194336 52.5714844,44.3198242 C53.158724,44.7202148 53.5824707,45.197347 53.8427246,45.7512207 C54.1029785,46.3050944 54.253125,46.8889974 54.2931641,47.5029297 C54.3332031,48.116862 54.3532227,48.6774089 54.3532227,49.1845703 C54.3532227,49.3046875 54.3332031,49.4881999 54.2931641,49.7351074 C54.253125,49.982015 54.1763835,50.2589518 54.0629395,50.565918 C53.9494954,50.8728841 53.7926758,51.1865234 53.5924805,51.5068359 C53.3922852,51.8271484 53.1353678,52.1174316 52.8217285,52.3776855 C52.5080892,52.6379395 52.1277181,52.8514811 51.6806152,53.0183105 C51.2335124,53.18514 50.709668,53.2618815 50.109082,53.2485352 L50.3617775,53.2487394 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M31.1318359,57.3125 L31.1318359,28.1240234 L26.9477539,28.1240234 L22.8037109,31.1469727 L22.8037109,35.5913086 L26.9477539,32.5483398 L26.9477539,57.3125 L31.1318359,57.3125 Z M48.787793,57.3125 L58.6774414,32.3081055 L58.6774414,28.1240234 L42.0612305,28.1240234 L42.0612305,36.5322266 L46.2453125,36.5322266 L46.2453125,32.3081055 L53.9928711,32.3081055 L44.1232422,57.3125 L48.787793,57.3125 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M31.1318359,57.3125 L31.1318359,28.1240234 L26.9477539,28.1240234 L22.8037109,31.1469727 L22.8037109,35.5913086 L26.9477539,32.5483398 L26.9477539,57.3125 L31.1318359,57.3125 Z M49.8087891,57.5527344 C52.3045573,57.5260417 54.4132812,56.7052409 56.1349609,55.090332 C57.8299479,53.5154622 58.6907878,51.4134115 58.7174805,48.7841797 C58.6907878,45.968099 57.5430013,43.7792969 55.2741211,42.2177734 C56.1416341,41.4570313 56.8489909,40.5828451 57.3961914,39.5952148 C57.9166992,38.6743164 58.1769531,37.5799154 58.1769531,36.3120117 C58.1502604,33.8829753 57.3561523,31.874349 55.7946289,30.2861328 C54.2464518,28.711263 52.2511719,27.9104818 49.8087891,27.8837891 C47.4064453,27.9104818 45.4445312,28.711263 43.9230469,30.2861328 C42.308138,31.874349 41.4873372,33.8829753 41.4606445,36.3120117 C41.4606445,37.5799154 41.7475911,38.6743164 42.3214844,39.5952148 C42.8152995,40.5828451 43.4959635,41.4570313 44.3634766,42.2177734 C42.0945964,43.7792969 40.9468099,45.968099 40.9201172,48.7841797 C40.9468099,51.4134115 41.8343424,53.5154622 43.5827148,55.090332 C45.2510091,56.7052409 47.3263672,57.5260417 49.8087891,57.5527344 Z M49.8087891,40.4160156 C48.7277344,40.4160156 47.7734701,40.0489909 46.9459961,39.3149414 C46.1051758,38.6075846 45.6714193,37.5799154 45.6447266,36.2319336 C45.6714193,34.8439128 46.1051758,33.796224 46.9459961,33.0888672 C47.7734701,32.4082031 48.7277344,32.0678711 49.8087891,32.0678711 C50.9298828,32.0678711 51.9041667,32.4082031 52.7316406,33.0888672 C53.5457682,33.796224 53.9661784,34.8439128 53.9928711,36.2319336 C53.9661784,37.5799154 53.5457682,38.6075846 52.7316406,39.3149414 C51.9041667,40.0489909 50.9298828,40.4160156 49.8087891,40.4160156 Z M49.8087891,53.3686523 C48.5275391,53.3686523 47.4398112,52.9215495 46.5456055,52.0273438 C45.6113607,51.1731771 45.1308919,50.078776 45.1041992,48.7441406 C45.1308919,47.3828125 45.6113607,46.2750651 46.5456055,45.4208984 C47.4398112,44.5800781 48.5275391,44.1463216 49.8087891,44.1196289 C51.1300781,44.1463216 52.2378255,44.5800781 53.1320312,45.4208984 C54.0395833,46.2750651 54.5067057,47.3828125 54.5333984,48.7441406 C54.5067057,50.078776 54.0395833,51.1731771 53.1320312,52.0273438 C52.2378255,52.9215495 51.1300781,53.3686523 49.8087891,53.3686523 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M31.1318359,57.3125 L31.1318359,28.1240234 L26.9477539,28.1240234 L22.8037109,31.1469727 L22.8037109,35.5913086 L26.9477539,32.5483398 L26.9477539,57.3125 L31.1318359,57.3125 Z M49.0880859,57.3125 C50.3559896,54.75 51.503776,52.4277344 52.5314453,50.3457031 C52.971875,49.4648438 53.4022949,48.587321 53.8227051,47.7131348 C54.2431152,46.8389486 54.6301595,46.0448405 54.9838379,45.3308105 C55.3375163,44.6167806 55.6311361,44.0128581 55.8646973,43.519043 C56.0982585,43.0252279 56.2417318,42.7049154 56.2951172,42.5581055 C56.5086589,42.0242513 56.6955078,41.5304362 56.8556641,41.0766602 C57.0158203,40.6228841 57.1592936,40.1624349 57.286084,39.6953125 C57.4128743,39.2281901 57.5229818,38.7377116 57.6164062,38.223877 C57.7098307,37.7100423 57.796582,37.1261393 57.8766602,36.472168 C57.9300456,36.0317383 57.9166992,35.5012207 57.8366211,34.8806152 C57.756543,34.2600098 57.5930501,33.6193848 57.3461426,32.9587402 C57.099235,32.2980957 56.7555664,31.6474609 56.3151367,31.0068359 C55.874707,30.3662109 55.3208333,29.8056641 54.6535156,29.3251953 C53.9861979,28.8447266 53.1887533,28.4777018 52.2611816,28.2241211 C51.33361,27.9705404 50.2625651,27.8971354 49.0480469,28.0039063 C47.8869141,28.1106771 46.8826009,28.3575846 46.0351074,28.7446289 C45.1876139,29.1316732 44.473584,29.5921224 43.8930176,30.1259766 C43.3124512,30.6598307 42.8419922,31.2303874 42.4816406,31.8376465 C42.1212891,32.4449056 41.8410156,33.0154622 41.6408203,33.5493164 C41.440625,34.0831706 41.3038249,34.5402832 41.2304199,34.9206543 C41.157015,35.3010254 41.1136393,35.5379232 41.100293,35.6313477 C41.0736003,35.8181966 41.0569173,36.0450846 41.0502441,36.3120117 C41.043571,36.5789388 41.0402344,36.8425293 41.0402344,37.1027832 C41.0402344,37.3630371 41.043571,37.5999349 41.0502441,37.8134766 C41.0569173,38.0270182 41.0602539,38.1738281 41.0602539,38.2539063 C41.0602539,38.507487 41.1036296,38.8578288 41.1903809,39.3049316 C41.2771322,39.7520345 41.4172689,40.2325033 41.610791,40.7463379 C41.8043132,41.2601725 42.0612305,41.7873535 42.381543,42.3278809 C42.7018555,42.8684082 43.1022461,43.3588867 43.5827148,43.7993164 C44.0631836,44.2397461 44.6337402,44.6067708 45.2943848,44.9003906 C45.9550293,45.1940104 46.7257812,45.3541667 47.6066406,45.3808594 C48.2472656,45.4075521 48.8111491,45.3975423 49.298291,45.3508301 C49.7854329,45.3041178 50.222526,45.1673177 50.6095703,44.9404297 C50.3559896,45.3808594 50.0256673,45.9881185 49.6186035,46.762207 C49.2115397,47.5362956 48.7744466,48.3837891 48.3073242,49.3046875 C47.8402018,50.2255859 47.3664062,51.1598307 46.8859375,52.1074219 C46.4054687,53.055013 45.9717122,53.9158529 45.584668,54.6899414 C45.1976237,55.4640299 44.8839844,56.0946452 44.64375,56.5817871 C44.4035156,57.068929 44.2900716,57.3125 44.303418,57.3125 L44.303418,57.3125 L49.0880859,57.3125 Z M49.5980279,41.7632559 L49.3483398,41.7573242 C48.3340169,41.7039388 47.546582,41.4803874 46.9860352,41.0866699 C46.4254883,40.6929525 46.0150879,40.2191569 45.754834,39.6652832 C45.4945801,39.1114095 45.3377604,38.5208333 45.284375,37.8935547 C45.2309896,37.266276 45.2042969,36.6923828 45.2042969,36.171875 C45.2042969,36.0117188 45.2643555,35.718099 45.3844727,35.2910156 C45.5045898,34.8639323 45.7181315,34.4235026 46.0250977,33.9697266 C46.3320638,33.5159505 46.7491374,33.1022135 47.2763184,32.7285156 C47.8034993,32.3548177 48.4808268,32.1479492 49.3083008,32.1079102 C50.0556966,32.0678711 50.6796387,32.1312663 51.180127,32.2980957 C51.6806152,32.4649251 52.0910156,32.6951497 52.4113281,32.9887695 C52.7316406,33.2823893 52.9785482,33.6260579 53.1520508,34.0197754 C53.3255534,34.4134928 53.4490072,34.8238932 53.5224121,35.2509766 C53.5958171,35.6780599 53.6358561,36.0984701 53.6425293,36.512207 C53.6492025,36.925944 53.6592122,37.2996419 53.6725586,37.6333008 C53.6725586,37.793457 53.6391927,38.0069987 53.5724609,38.2739258 C53.5057292,38.5408529 53.3956217,38.8311361 53.2421387,39.1447754 C53.0886556,39.4584147 52.8951335,39.7753906 52.6615723,40.0957031 C52.4280111,40.4160156 52.1510742,40.7029622 51.8307617,40.956543 C51.5104492,41.2101237 51.1434245,41.4136556 50.7296875,41.5671387 C50.3159505,41.7206217 49.8555013,41.7840169 49.3483398,41.7573242 L49.5980279,41.7632559 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M36.9575195,57.3125 L36.9575195,53.128418 L25.8066406,53.128418 L35.1958008,41.3569336 C36.3702799,39.8621419 36.9575195,38.1538086 36.9575195,36.2319336 C36.9308268,33.8295898 36.1233724,31.8476563 34.5351562,30.2861328 C32.9736328,28.711263 30.9383138,27.9104818 28.4291992,27.8837891 C26.1870117,27.9104818 24.2985026,28.6979167 22.7636719,30.2460938 C21.2421875,31.8343099 20.4280599,33.8429362 20.3212891,36.2719727 L24.4853516,36.2719727 C24.6321615,34.9373372 25.0992839,33.9029948 25.8867187,33.1689453 C26.6474609,32.4348958 27.6017253,32.0678711 28.7495117,32.0678711 C30.0441081,32.0945638 31.0450846,32.514974 31.7524414,33.3291016 C32.4331055,34.1432292 32.7734375,35.0974935 32.7734375,36.1918945 C32.7734375,36.6056315 32.7200521,37.0460612 32.6132812,37.5131836 C32.453125,38.0069987 32.152832,38.5408529 31.7124023,39.1147461 L20.3212891,53.3686523 L20.3212891,57.3125 L36.9575195,57.3125 Z M49.8087891,57.5527344 C52.0776693,57.5260417 54.0062174,56.7919922 55.5944336,55.3505859 C57.2093424,53.8958333 58.0434896,51.8271484 58.096875,49.1445313 L58.096875,36.2719727 C58.0434896,33.5760091 57.2093424,31.500651 55.5944336,30.0458984 C54.0062174,28.6044922 52.0776693,27.8837891 49.8087891,27.8837891 C47.6066406,27.8837891 45.7114583,28.6044922 44.1232422,30.0458984 C42.4282552,31.500651 41.5674154,33.5760091 41.5407227,36.2719727 L41.5407227,49.1445313 C41.5674154,51.8271484 42.4282552,53.8958333 44.1232422,55.3505859 C45.7114583,56.7919922 47.6066406,57.5260417 49.8087891,57.5527344 Z M49.8087891,53.3686523 C47.1395182,53.3419596 45.7781901,51.8538411 45.7248047,48.9042969 L45.7248047,36.5322266 C45.7781901,33.609375 47.1395182,32.1212565 49.8087891,32.0678711 C52.4647135,32.1212565 53.8327148,33.609375 53.912793,36.5322266 L53.912793,48.9042969 C53.8327148,51.8538411 52.4647135,53.3419596 49.8087891,53.3686523 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M36.9575195,57.3125 L36.9575195,53.128418 L25.8066406,53.128418 L35.1958008,41.3569336 C36.3702799,39.8621419 36.9575195,38.1538086 36.9575195,36.2319336 C36.9308268,33.8295898 36.1233724,31.8476563 34.5351562,30.2861328 C32.9736328,28.711263 30.9383138,27.9104818 28.4291992,27.8837891 C26.1870117,27.9104818 24.2985026,28.6979167 22.7636719,30.2460938 C21.2421875,31.8343099 20.4280599,33.8429362 20.3212891,36.2719727 L24.4853516,36.2719727 C24.6321615,34.9373372 25.0992839,33.9029948 25.8867187,33.1689453 C26.6474609,32.4348958 27.6017253,32.0678711 28.7495117,32.0678711 C30.0441081,32.0945638 31.0450846,32.514974 31.7524414,33.3291016 C32.4331055,34.1432292 32.7734375,35.0974935 32.7734375,36.1918945 C32.7734375,36.6056315 32.7200521,37.0460612 32.6132812,37.5131836 C32.453125,38.0069987 32.152832,38.5408529 31.7124023,39.1147461 L20.3212891,53.3686523 L20.3212891,57.3125 L36.9575195,57.3125 Z M52.1110352,57.3125 L52.1110352,28.1240234 L47.9269531,28.1240234 L43.7829102,31.1469727 L43.7829102,35.5913086 L47.9269531,32.5483398 L47.9269531,57.3125 L52.1110352,57.3125 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M36.9575195,57.3125 L36.9575195,53.128418 L25.8066406,53.128418 L35.1958008,41.3569336 C36.3702799,39.8621419 36.9575195,38.1538086 36.9575195,36.2319336 C36.9308268,33.8295898 36.1233724,31.8476563 34.5351562,30.2861328 C32.9736328,28.711263 30.9383138,27.9104818 28.4291992,27.8837891 C26.1870117,27.9104818 24.2985026,28.6979167 22.7636719,30.2460938 C21.2421875,31.8343099 20.4280599,33.8429362 20.3212891,36.2719727 L24.4853516,36.2719727 C24.6321615,34.9373372 25.0992839,33.9029948 25.8867187,33.1689453 C26.6474609,32.4348958 27.6017253,32.0678711 28.7495117,32.0678711 C30.0441081,32.0945638 31.0450846,32.514974 31.7524414,33.3291016 C32.4331055,34.1432292 32.7734375,35.0974935 32.7734375,36.1918945 C32.7734375,36.6056315 32.7200521,37.0460612 32.6132812,37.5131836 C32.453125,38.0069987 32.152832,38.5408529 31.7124023,39.1147461 L20.3212891,53.3686523 L20.3212891,57.3125 L36.9575195,57.3125 Z M57.9367187,57.3125 L57.9367187,53.128418 L46.7858398,53.128418 L56.175,41.3569336 C57.3494792,39.8621419 57.9367187,38.1538086 57.9367187,36.2319336 C57.910026,33.8295898 57.1025716,31.8476563 55.5143555,30.2861328 C53.952832,28.711263 51.917513,27.9104818 49.4083984,27.8837891 C47.1662109,27.9104818 45.2777018,28.6979167 43.7428711,30.2460938 C42.2213867,31.8343099 41.4072591,33.8429362 41.3004883,36.2719727 L45.4645508,36.2719727 C45.6113607,34.9373372 46.0784831,33.9029948 46.865918,33.1689453 C47.6266602,32.4348958 48.5809245,32.0678711 49.7287109,32.0678711 C51.0233073,32.0945638 52.0242839,32.514974 52.7316406,33.3291016 C53.4123047,34.1432292 53.7526367,35.0974935 53.7526367,36.1918945 C53.7526367,36.6056315 53.6992513,37.0460612 53.5924805,37.5131836 C53.4323242,38.0069987 53.1320312,38.5408529 52.6916016,39.1147461 L41.3004883,53.3686523 L41.3004883,57.3125 L57.9367187,57.3125 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M36.9575195,57.3125 L36.9575195,53.128418 L25.8066406,53.128418 L35.1958008,41.3569336 C36.3702799,39.8621419 36.9575195,38.1538086 36.9575195,36.2319336 C36.9308268,33.8295898 36.1233724,31.8476563 34.5351562,30.2861328 C32.9736328,28.711263 30.9383138,27.9104818 28.4291992,27.8837891 C26.1870117,27.9104818 24.2985026,28.6979167 22.7636719,30.2460938 C21.2421875,31.8343099 20.4280599,33.8429362 20.3212891,36.2719727 L24.4853516,36.2719727 C24.6321615,34.9373372 25.0992839,33.9029948 25.8867187,33.1689453 C26.6474609,32.4348958 27.6017253,32.0678711 28.7495117,32.0678711 C30.0441081,32.0945638 31.0450846,32.514974 31.7524414,33.3291016 C32.4331055,34.1432292 32.7734375,35.0974935 32.7734375,36.1918945 C32.7734375,36.6056315 32.7200521,37.0460612 32.6132812,37.5131836 C32.453125,38.0069987 32.152832,38.5408529 31.7124023,39.1147461 L20.3212891,53.3686523 L20.3212891,57.3125 L36.9575195,57.3125 Z M48.787793,57.5527344 C51.3636393,57.5260417 53.4723633,56.7052409 55.1139648,55.090332 C56.7822591,53.5154622 57.6297526,51.360026 57.6564453,48.6240234 C57.6564453,47.3961589 57.3961914,46.2216797 56.8756836,45.1005859 C56.3284831,43.9794922 55.4409505,43.0185547 54.2130859,42.2177734 C55.4142578,41.4036458 56.2484049,40.4760742 56.7155273,39.4350586 C57.115918,38.394043 57.3161133,37.3129883 57.3161133,36.1918945 C57.2894206,33.9763997 56.5286784,32.0545247 55.0338867,30.4262695 C53.4723633,28.7579753 51.3636393,27.9104818 48.7077148,27.8837891 C46.6123372,27.9104818 44.8105794,28.6578776 43.3024414,30.1259766 C41.7676107,31.6074219 40.8934245,33.4291992 40.6798828,35.5913086 L44.8639648,35.5913086 C45.1308919,34.3901367 45.6513997,33.5026042 46.4254883,32.9287109 C47.1595378,32.3548177 47.9736654,32.0678711 48.8678711,32.0678711 C50.109082,32.0945638 51.1234049,32.4882813 51.9108398,33.2490234 C52.6982747,34.0364583 53.1053385,35.0441081 53.1320312,36.2719727 C53.1320312,37.4864909 52.7383138,38.4741211 51.9508789,39.2348633 C51.163444,40.0222982 50.0556966,40.4160156 48.6276367,40.4160156 L47.1862305,40.4160156 L47.1862305,44.1196289 L48.9479492,44.1196289 C50.2425456,44.1196289 51.3102539,44.5133464 52.1510742,45.3007813 C53.0052409,46.1149089 53.4456706,47.2626953 53.4723633,48.7441406 C53.4456706,50.1988932 53.0052409,51.319987 52.1510742,52.1074219 C51.3102539,52.9482422 50.2759115,53.3686523 49.0480469,53.3686523 C47.9002604,53.3686523 46.979362,53.055013 46.2853516,52.4277344 C45.6046875,51.8271484 45.0775065,51.0397135 44.7038086,50.0654297 L40.5197266,50.0654297 C40.9735026,52.4944661 41.967806,54.3496094 43.5026367,55.6308594 C45.0374674,56.9121094 46.7991862,57.5527344 48.787793,57.5527344 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M36.9575195,57.3125 L36.9575195,53.128418 L25.8066406,53.128418 L35.1958008,41.3569336 C36.3702799,39.8621419 36.9575195,38.1538086 36.9575195,36.2319336 C36.9308268,33.8295898 36.1233724,31.8476563 34.5351562,30.2861328 C32.9736328,28.711263 30.9383138,27.9104818 28.4291992,27.8837891 C26.1870117,27.9104818 24.2985026,28.6979167 22.7636719,30.2460938 C21.2421875,31.8343099 20.4280599,33.8429362 20.3212891,36.2719727 L24.4853516,36.2719727 C24.6321615,34.9373372 25.0992839,33.9029948 25.8867187,33.1689453 C26.6474609,32.4348958 27.6017253,32.0678711 28.7495117,32.0678711 C30.0441081,32.0945638 31.0450846,32.514974 31.7524414,33.3291016 C32.4331055,34.1432292 32.7734375,35.0974935 32.7734375,36.1918945 C32.7734375,36.6056315 32.7200521,37.0460612 32.6132812,37.5131836 C32.453125,38.0069987 32.152832,38.5408529 31.7124023,39.1147461 L20.3212891,53.3686523 L20.3212891,57.3125 L36.9575195,57.3125 Z M56.8356445,57.3125 L56.8356445,52.9682617 L59.1579102,52.9682617 L59.1579102,49.0244141 L56.8356445,49.0244141 L56.8356445,40.6362305 L52.6515625,40.6362305 L52.6515625,49.0244141 L45.0241211,49.0244141 L54.89375,28.1240234 L50.2291992,28.1240234 L40.4796875,49.0244141 L40.4796875,52.9682617 L52.6515625,52.9682617 L52.6515625,57.3125 L56.8356445,57.3125 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M36.9575195,57.3125 L36.9575195,53.128418 L25.8066406,53.128418 L35.1958008,41.3569336 C36.3702799,39.8621419 36.9575195,38.1538086 36.9575195,36.2319336 C36.9308268,33.8295898 36.1233724,31.8476563 34.5351562,30.2861328 C32.9736328,28.711263 30.9383138,27.9104818 28.4291992,27.8837891 C26.1870117,27.9104818 24.2985026,28.6979167 22.7636719,30.2460938 C21.2421875,31.8343099 20.4280599,33.8429362 20.3212891,36.2719727 L24.4853516,36.2719727 C24.6321615,34.9373372 25.0992839,33.9029948 25.8867187,33.1689453 C26.6474609,32.4348958 27.6017253,32.0678711 28.7495117,32.0678711 C30.0441081,32.0945638 31.0450846,32.514974 31.7524414,33.3291016 C32.4331055,34.1432292 32.7734375,35.0974935 32.7734375,36.1918945 C32.7734375,36.6056315 32.7200521,37.0460612 32.6132812,37.5131836 C32.453125,38.0069987 32.152832,38.5408529 31.7124023,39.1147461 L20.3212891,53.3686523 L20.3212891,57.3125 L36.9575195,57.3125 Z M50.0290039,57.5527344 C51.4971029,57.5260417 52.7650065,57.1857096 53.8327148,56.5317383 C54.9271159,55.9044596 55.761263,55.2104492 56.3351562,54.449707 C56.9891276,53.6756185 57.45625,52.7880859 57.7365234,51.7871094 C58.0034505,50.7727865 58.1369141,49.3180339 58.1369141,47.4228516 C58.1369141,46.194987 58.096875,45.1606445 58.0167969,44.3198242 C57.9367187,43.4923503 57.8032552,42.7916667 57.6164062,42.2177734 C57.2560547,41.1634115 56.6688151,40.2358398 55.8546875,39.4350586 C55.307487,38.8344727 54.593457,38.3273112 53.7125977,37.9135742 C52.7783529,37.5398763 51.7506836,37.339681 50.6295898,37.3129883 C48.8812174,37.3129883 47.3263672,37.8468424 45.9650391,38.9145508 L45.9650391,32.3081055 L57.5763672,32.3081055 L57.5763672,28.1240234 L42.0211914,28.1240234 L42.0211914,43.8994141 L45.9650391,43.8994141 C46.4588542,43.0052083 47.019401,42.3779297 47.6466797,42.0175781 C48.2739583,41.6705729 48.9679687,41.4970703 49.7287109,41.4970703 C50.5828776,41.4970703 51.2969076,41.6171875 51.8708008,41.8574219 C52.444694,42.1510417 52.8784505,42.5447591 53.1720703,43.0385742 C53.7192708,44.0795898 53.9795247,45.4342448 53.952832,47.1025391 C53.952832,47.7832031 53.9394857,48.4838867 53.912793,49.2045898 C53.8594076,49.9386393 53.7125977,50.6193034 53.4723633,51.246582 C53.2454753,51.8738607 52.8450846,52.3810221 52.2711914,52.7680664 C51.6706055,53.168457 50.8698242,53.3686523 49.8688477,53.3686523 C47.5332357,53.3419596 46.1385417,52.2075195 45.6847656,49.965332 L41.5006836,49.965332 C41.9010742,52.6479492 42.9354167,54.5898438 44.6037109,55.7910156 C46.2453125,56.9654948 48.0537435,57.5527344 50.0290039,57.5527344 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M36.9575195,57.3125 L36.9575195,53.128418 L25.8066406,53.128418 L35.1958008,41.3569336 C36.3702799,39.8621419 36.9575195,38.1538086 36.9575195,36.2319336 C36.9308268,33.8295898 36.1233724,31.8476563 34.5351562,30.2861328 C32.9736328,28.711263 30.9383138,27.9104818 28.4291992,27.8837891 C26.1870117,27.9104818 24.2985026,28.6979167 22.7636719,30.2460938 C21.2421875,31.8343099 20.4280599,33.8429362 20.3212891,36.2719727 L24.4853516,36.2719727 C24.6321615,34.9373372 25.0992839,33.9029948 25.8867187,33.1689453 C26.6474609,32.4348958 27.6017253,32.0678711 28.7495117,32.0678711 C30.0441081,32.0945638 31.0450846,32.514974 31.7524414,33.3291016 C32.4331055,34.1432292 32.7734375,35.0974935 32.7734375,36.1918945 C32.7734375,36.6056315 32.7200521,37.0460612 32.6132812,37.5131836 C32.453125,38.0069987 32.152832,38.5408529 31.7124023,39.1147461 L20.3212891,53.3686523 L20.3212891,57.3125 L36.9575195,57.3125 Z M47.3063477,57.2924805 C48.2405924,57.5594076 49.314974,57.6394857 50.5294922,57.5327148 C51.690625,57.4392904 52.6916016,57.1923828 53.5324219,56.7919922 C54.3732422,56.3916016 55.0872721,55.9144694 55.6745117,55.3605957 C56.2617513,54.806722 56.7355469,54.2194824 57.0958984,53.598877 C57.45625,52.9782715 57.7331868,52.3977051 57.926709,51.8571777 C58.1202311,51.3166504 58.2536947,50.849528 58.3270996,50.4558105 C58.4005046,50.0620931 58.4438802,49.8185221 58.4572266,49.7250977 C58.4705729,49.5382487 58.4805827,49.3113607 58.4872559,49.0444336 C58.493929,48.7775065 58.4972656,48.513916 58.4972656,48.2536621 L58.4972656,48.2536621 L58.4972656,47.1025391 C58.4972656,46.8489583 58.4605632,46.4986165 58.3871582,46.0515137 C58.3137533,45.6044108 58.1836263,45.1239421 57.9967773,44.6101074 C57.8099284,44.0962728 57.5530111,43.5690918 57.2260254,43.0285645 C56.8990397,42.4880371 56.4886393,41.9975586 55.9948242,41.5571289 C55.5010091,41.1166992 54.9104329,40.7496745 54.2230957,40.4560547 C53.5357585,40.1624349 52.7316406,40.0022786 51.8107422,39.9755859 C51.1701172,39.9488932 50.6062337,39.958903 50.1190918,40.0056152 C49.6319499,40.0523275 49.1948568,40.1891276 48.8078125,40.4160156 C48.9946615,40.0957031 49.2282227,39.6686198 49.5084961,39.1347656 C49.7887695,38.6009115 50.0990723,38.0069987 50.4394043,37.3530273 C50.7797363,36.699056 51.1334147,36.0117188 51.5004395,35.2910156 C51.8674642,34.5703125 52.2278158,33.8562826 52.5814941,33.1489258 L52.5814941,33.1489258 L53.0994995,32.112915 C53.2679972,31.7759196 53.4323242,31.4472656 53.5924805,31.1269531 C53.912793,30.4863281 54.1930664,29.9191081 54.4333008,29.425293 C54.6735352,28.9314779 54.8637207,28.541097 55.0038574,28.2541504 C55.1439941,27.9672038 55.2073893,27.8237305 55.194043,27.8237305 L55.194043,27.8237305 L50.4694336,27.8237305 C49.1748372,30.4262695 48.0070312,32.781901 46.9660156,34.890625 C46.5255859,35.7981771 46.0851562,36.6923828 45.6447266,37.5732422 C45.2042969,38.4541016 44.8072428,39.261556 44.4535645,39.9956055 C44.0998861,40.7296549 43.8029297,41.3469238 43.5626953,41.8474121 C43.3224609,42.3479004 43.175651,42.6648763 43.1222656,42.7983398 C42.908724,43.332194 42.721875,43.8260091 42.5617187,44.2797852 C42.4015625,44.7335612 42.2580892,45.1940104 42.1312988,45.6611328 C42.0045085,46.1282552 41.894401,46.6187337 41.8009766,47.1325684 C41.7075521,47.646403 41.6208008,48.230306 41.5407227,48.8842773 C41.4873372,49.324707 41.5040202,49.8585612 41.5907715,50.4858398 C41.6775228,51.1131185 41.8510254,51.7670898 42.1112793,52.4477539 C42.3715332,53.128418 42.7285482,53.7923991 43.1823242,54.4396973 C43.6361003,55.0869954 44.2033203,55.6575521 44.8839844,56.1513672 C45.5646484,56.6451823 46.3721029,57.0255534 47.3063477,57.2924805 Z M50.3617775,53.2487394 L50.109082,53.2485352 C49.3616862,53.2485352 48.7377441,53.1551107 48.2372559,52.9682617 C47.7367676,52.7814128 47.3263672,52.5411784 47.0060547,52.2475586 C46.6857422,51.9539388 46.4388346,51.6169434 46.265332,51.2365723 C46.0918294,50.8562012 45.9683757,50.4624837 45.8949707,50.0554199 C45.8215658,49.6483561 45.7781901,49.2412923 45.7648437,48.8342285 C45.7514974,48.4271647 45.7448242,48.0568034 45.7448242,47.7231445 C45.7448242,47.5763346 45.7781901,47.3661296 45.8449219,47.0925293 C45.9116536,46.818929 46.0217611,46.5219727 46.1752441,46.2016602 C46.3287272,45.8813477 46.5222493,45.5576986 46.7558105,45.2307129 C46.9893717,44.9037272 47.2663086,44.613444 47.5866211,44.3598633 C47.9069336,44.1062826 48.2739583,43.9060872 48.6876953,43.7592773 C49.1014323,43.6124674 49.5618815,43.559082 50.069043,43.5991211 C51.1500977,43.6791992 51.9842448,43.9194336 52.5714844,44.3198242 C53.158724,44.7202148 53.5824707,45.197347 53.8427246,45.7512207 C54.1029785,46.3050944 54.253125,46.8889974 54.2931641,47.5029297 C54.3332031,48.116862 54.3532227,48.6774089 54.3532227,49.1845703 C54.3532227,49.3046875 54.3332031,49.4881999 54.2931641,49.7351074 C54.253125,49.982015 54.1763835,50.2589518 54.0629395,50.565918 C53.9494954,50.8728841 53.7926758,51.1865234 53.5924805,51.5068359 C53.3922852,51.8271484 53.1353678,52.1174316 52.8217285,52.3776855 C52.5080892,52.6379395 52.1277181,52.8514811 51.6806152,53.0183105 C51.2335124,53.18514 50.709668,53.2618815 50.109082,53.2485352 L50.3617775,53.2487394 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M36.9575195,57.3125 L36.9575195,53.128418 L25.8066406,53.128418 L35.1958008,41.3569336 C36.3702799,39.8621419 36.9575195,38.1538086 36.9575195,36.2319336 C36.9308268,33.8295898 36.1233724,31.8476563 34.5351562,30.2861328 C32.9736328,28.711263 30.9383138,27.9104818 28.4291992,27.8837891 C26.1870117,27.9104818 24.2985026,28.6979167 22.7636719,30.2460938 C21.2421875,31.8343099 20.4280599,33.8429362 20.3212891,36.2719727 L24.4853516,36.2719727 C24.6321615,34.9373372 25.0992839,33.9029948 25.8867187,33.1689453 C26.6474609,32.4348958 27.6017253,32.0678711 28.7495117,32.0678711 C30.0441081,32.0945638 31.0450846,32.514974 31.7524414,33.3291016 C32.4331055,34.1432292 32.7734375,35.0974935 32.7734375,36.1918945 C32.7734375,36.6056315 32.7200521,37.0460612 32.6132812,37.5131836 C32.453125,38.0069987 32.152832,38.5408529 31.7124023,39.1147461 L20.3212891,53.3686523 L20.3212891,57.3125 L36.9575195,57.3125 Z M48.787793,57.3125 L58.6774414,32.3081055 L58.6774414,28.1240234 L42.0612305,28.1240234 L42.0612305,36.5322266 L46.2453125,36.5322266 L46.2453125,32.3081055 L53.9928711,32.3081055 L44.1232422,57.3125 L48.787793,57.3125 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M36.9575195,57.3125 L36.9575195,53.128418 L25.8066406,53.128418 L35.1958008,41.3569336 C36.3702799,39.8621419 36.9575195,38.1538086 36.9575195,36.2319336 C36.9308268,33.8295898 36.1233724,31.8476563 34.5351562,30.2861328 C32.9736328,28.711263 30.9383138,27.9104818 28.4291992,27.8837891 C26.1870117,27.9104818 24.2985026,28.6979167 22.7636719,30.2460938 C21.2421875,31.8343099 20.4280599,33.8429362 20.3212891,36.2719727 L24.4853516,36.2719727 C24.6321615,34.9373372 25.0992839,33.9029948 25.8867187,33.1689453 C26.6474609,32.4348958 27.6017253,32.0678711 28.7495117,32.0678711 C30.0441081,32.0945638 31.0450846,32.514974 31.7524414,33.3291016 C32.4331055,34.1432292 32.7734375,35.0974935 32.7734375,36.1918945 C32.7734375,36.6056315 32.7200521,37.0460612 32.6132812,37.5131836 C32.453125,38.0069987 32.152832,38.5408529 31.7124023,39.1147461 L20.3212891,53.3686523 L20.3212891,57.3125 L36.9575195,57.3125 Z M49.8087891,57.5527344 C52.3045573,57.5260417 54.4132812,56.7052409 56.1349609,55.090332 C57.8299479,53.5154622 58.6907878,51.4134115 58.7174805,48.7841797 C58.6907878,45.968099 57.5430013,43.7792969 55.2741211,42.2177734 C56.1416341,41.4570313 56.8489909,40.5828451 57.3961914,39.5952148 C57.9166992,38.6743164 58.1769531,37.5799154 58.1769531,36.3120117 C58.1502604,33.8829753 57.3561523,31.874349 55.7946289,30.2861328 C54.2464518,28.711263 52.2511719,27.9104818 49.8087891,27.8837891 C47.4064453,27.9104818 45.4445312,28.711263 43.9230469,30.2861328 C42.308138,31.874349 41.4873372,33.8829753 41.4606445,36.3120117 C41.4606445,37.5799154 41.7475911,38.6743164 42.3214844,39.5952148 C42.8152995,40.5828451 43.4959635,41.4570313 44.3634766,42.2177734 C42.0945964,43.7792969 40.9468099,45.968099 40.9201172,48.7841797 C40.9468099,51.4134115 41.8343424,53.5154622 43.5827148,55.090332 C45.2510091,56.7052409 47.3263672,57.5260417 49.8087891,57.5527344 Z M49.8087891,40.4160156 C48.7277344,40.4160156 47.7734701,40.0489909 46.9459961,39.3149414 C46.1051758,38.6075846 45.6714193,37.5799154 45.6447266,36.2319336 C45.6714193,34.8439128 46.1051758,33.796224 46.9459961,33.0888672 C47.7734701,32.4082031 48.7277344,32.0678711 49.8087891,32.0678711 C50.9298828,32.0678711 51.9041667,32.4082031 52.7316406,33.0888672 C53.5457682,33.796224 53.9661784,34.8439128 53.9928711,36.2319336 C53.9661784,37.5799154 53.5457682,38.6075846 52.7316406,39.3149414 C51.9041667,40.0489909 50.9298828,40.4160156 49.8087891,40.4160156 Z M49.8087891,53.3686523 C48.5275391,53.3686523 47.4398112,52.9215495 46.5456055,52.0273438 C45.6113607,51.1731771 45.1308919,50.078776 45.1041992,48.7441406 C45.1308919,47.3828125 45.6113607,46.2750651 46.5456055,45.4208984 C47.4398112,44.5800781 48.5275391,44.1463216 49.8087891,44.1196289 C51.1300781,44.1463216 52.2378255,44.5800781 53.1320312,45.4208984 C54.0395833,46.2750651 54.5067057,47.3828125 54.5333984,48.7441406 C54.5067057,50.078776 54.0395833,51.1731771 53.1320312,52.0273438 C52.2378255,52.9215495 51.1300781,53.3686523 49.8087891,53.3686523 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M36.9575195,57.3125 L36.9575195,53.128418 L25.8066406,53.128418 L35.1958008,41.3569336 C36.3702799,39.8621419 36.9575195,38.1538086 36.9575195,36.2319336 C36.9308268,33.8295898 36.1233724,31.8476563 34.5351562,30.2861328 C32.9736328,28.711263 30.9383138,27.9104818 28.4291992,27.8837891 C26.1870117,27.9104818 24.2985026,28.6979167 22.7636719,30.2460938 C21.2421875,31.8343099 20.4280599,33.8429362 20.3212891,36.2719727 L24.4853516,36.2719727 C24.6321615,34.9373372 25.0992839,33.9029948 25.8867187,33.1689453 C26.6474609,32.4348958 27.6017253,32.0678711 28.7495117,32.0678711 C30.0441081,32.0945638 31.0450846,32.514974 31.7524414,33.3291016 C32.4331055,34.1432292 32.7734375,35.0974935 32.7734375,36.1918945 C32.7734375,36.6056315 32.7200521,37.0460612 32.6132812,37.5131836 C32.453125,38.0069987 32.152832,38.5408529 31.7124023,39.1147461 L20.3212891,53.3686523 L20.3212891,57.3125 L36.9575195,57.3125 Z M49.0880859,57.3125 C50.3559896,54.75 51.503776,52.4277344 52.5314453,50.3457031 C52.971875,49.4648438 53.4022949,48.587321 53.8227051,47.7131348 C54.2431152,46.8389486 54.6301595,46.0448405 54.9838379,45.3308105 C55.3375163,44.6167806 55.6311361,44.0128581 55.8646973,43.519043 C56.0982585,43.0252279 56.2417318,42.7049154 56.2951172,42.5581055 C56.5086589,42.0242513 56.6955078,41.5304362 56.8556641,41.0766602 C57.0158203,40.6228841 57.1592936,40.1624349 57.286084,39.6953125 C57.4128743,39.2281901 57.5229818,38.7377116 57.6164062,38.223877 C57.7098307,37.7100423 57.796582,37.1261393 57.8766602,36.472168 C57.9300456,36.0317383 57.9166992,35.5012207 57.8366211,34.8806152 C57.756543,34.2600098 57.5930501,33.6193848 57.3461426,32.9587402 C57.099235,32.2980957 56.7555664,31.6474609 56.3151367,31.0068359 C55.874707,30.3662109 55.3208333,29.8056641 54.6535156,29.3251953 C53.9861979,28.8447266 53.1887533,28.4777018 52.2611816,28.2241211 C51.33361,27.9705404 50.2625651,27.8971354 49.0480469,28.0039063 C47.8869141,28.1106771 46.8826009,28.3575846 46.0351074,28.7446289 C45.1876139,29.1316732 44.473584,29.5921224 43.8930176,30.1259766 C43.3124512,30.6598307 42.8419922,31.2303874 42.4816406,31.8376465 C42.1212891,32.4449056 41.8410156,33.0154622 41.6408203,33.5493164 C41.440625,34.0831706 41.3038249,34.5402832 41.2304199,34.9206543 C41.157015,35.3010254 41.1136393,35.5379232 41.100293,35.6313477 C41.0736003,35.8181966 41.0569173,36.0450846 41.0502441,36.3120117 C41.043571,36.5789388 41.0402344,36.8425293 41.0402344,37.1027832 C41.0402344,37.3630371 41.043571,37.5999349 41.0502441,37.8134766 C41.0569173,38.0270182 41.0602539,38.1738281 41.0602539,38.2539063 C41.0602539,38.507487 41.1036296,38.8578288 41.1903809,39.3049316 C41.2771322,39.7520345 41.4172689,40.2325033 41.610791,40.7463379 C41.8043132,41.2601725 42.0612305,41.7873535 42.381543,42.3278809 C42.7018555,42.8684082 43.1022461,43.3588867 43.5827148,43.7993164 C44.0631836,44.2397461 44.6337402,44.6067708 45.2943848,44.9003906 C45.9550293,45.1940104 46.7257812,45.3541667 47.6066406,45.3808594 C48.2472656,45.4075521 48.8111491,45.3975423 49.298291,45.3508301 C49.7854329,45.3041178 50.222526,45.1673177 50.6095703,44.9404297 C50.3559896,45.3808594 50.0256673,45.9881185 49.6186035,46.762207 C49.2115397,47.5362956 48.7744466,48.3837891 48.3073242,49.3046875 C47.8402018,50.2255859 47.3664062,51.1598307 46.8859375,52.1074219 C46.4054687,53.055013 45.9717122,53.9158529 45.584668,54.6899414 C45.1976237,55.4640299 44.8839844,56.0946452 44.64375,56.5817871 C44.4035156,57.068929 44.2900716,57.3125 44.303418,57.3125 L44.303418,57.3125 L49.0880859,57.3125 Z M49.5980279,41.7632559 L49.3483398,41.7573242 C48.3340169,41.7039388 47.546582,41.4803874 46.9860352,41.0866699 C46.4254883,40.6929525 46.0150879,40.2191569 45.754834,39.6652832 C45.4945801,39.1114095 45.3377604,38.5208333 45.284375,37.8935547 C45.2309896,37.266276 45.2042969,36.6923828 45.2042969,36.171875 C45.2042969,36.0117188 45.2643555,35.718099 45.3844727,35.2910156 C45.5045898,34.8639323 45.7181315,34.4235026 46.0250977,33.9697266 C46.3320638,33.5159505 46.7491374,33.1022135 47.2763184,32.7285156 C47.8034993,32.3548177 48.4808268,32.1479492 49.3083008,32.1079102 C50.0556966,32.0678711 50.6796387,32.1312663 51.180127,32.2980957 C51.6806152,32.4649251 52.0910156,32.6951497 52.4113281,32.9887695 C52.7316406,33.2823893 52.9785482,33.6260579 53.1520508,34.0197754 C53.3255534,34.4134928 53.4490072,34.8238932 53.5224121,35.2509766 C53.5958171,35.6780599 53.6358561,36.0984701 53.6425293,36.512207 C53.6492025,36.925944 53.6592122,37.2996419 53.6725586,37.6333008 C53.6725586,37.793457 53.6391927,38.0069987 53.5724609,38.2739258 C53.5057292,38.5408529 53.3956217,38.8311361 53.2421387,39.1447754 C53.0886556,39.4584147 52.8951335,39.7753906 52.6615723,40.0957031 C52.4280111,40.4160156 52.1510742,40.7029622 51.8307617,40.956543 C51.5104492,41.2101237 51.1434245,41.4136556 50.7296875,41.5671387 C50.3159505,41.7206217 49.8555013,41.7840169 49.3483398,41.7573242 L49.5980279,41.7632559 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M27.8085937,57.5527344 C30.3844401,57.5260417 32.4931641,56.7052409 34.1347656,55.090332 C35.8030599,53.5154622 36.6505534,51.360026 36.6772461,48.6240234 C36.6772461,47.3961589 36.4169922,46.2216797 35.8964844,45.1005859 C35.3492839,43.9794922 34.4617513,43.0185547 33.2338867,42.2177734 C34.4350586,41.4036458 35.2692057,40.4760742 35.7363281,39.4350586 C36.1367187,38.394043 36.3369141,37.3129883 36.3369141,36.1918945 C36.3102214,33.9763997 35.5494792,32.0545247 34.0546875,30.4262695 C32.4931641,28.7579753 30.3844401,27.9104818 27.7285156,27.8837891 C25.633138,27.9104818 23.8313802,28.6578776 22.3232422,30.1259766 C20.7884115,31.6074219 19.9142253,33.4291992 19.7006836,35.5913086 L23.8847656,35.5913086 C24.1516927,34.3901367 24.6722005,33.5026042 25.4462891,32.9287109 C26.1803385,32.3548177 26.9944661,32.0678711 27.8886719,32.0678711 C29.1298828,32.0945638 30.1442057,32.4882813 30.9316406,33.2490234 C31.7190755,34.0364583 32.1261393,35.0441081 32.152832,36.2719727 C32.152832,37.4864909 31.7591146,38.4741211 30.9716797,39.2348633 C30.1842448,40.0222982 29.0764974,40.4160156 27.6484375,40.4160156 L26.2070312,40.4160156 L26.2070312,44.1196289 L27.96875,44.1196289 C29.2633464,44.1196289 30.3310547,44.5133464 31.171875,45.3007813 C32.0260417,46.1149089 32.4664714,47.2626953 32.4931641,48.7441406 C32.4664714,50.1988932 32.0260417,51.319987 31.171875,52.1074219 C30.3310547,52.9482422 29.2967122,53.3686523 28.0688477,53.3686523 C26.9210612,53.3686523 26.0001628,53.055013 25.3061523,52.4277344 C24.6254883,51.8271484 24.0983073,51.0397135 23.7246094,50.0654297 L19.5405273,50.0654297 C19.9943034,52.4944661 20.9886068,54.3496094 22.5234375,55.6308594 C24.0582682,56.9121094 25.819987,57.5527344 27.8085937,57.5527344 Z M49.8087891,57.5527344 C52.0776693,57.5260417 54.0062174,56.7919922 55.5944336,55.3505859 C57.2093424,53.8958333 58.0434896,51.8271484 58.096875,49.1445313 L58.096875,36.2719727 C58.0434896,33.5760091 57.2093424,31.500651 55.5944336,30.0458984 C54.0062174,28.6044922 52.0776693,27.8837891 49.8087891,27.8837891 C47.6066406,27.8837891 45.7114583,28.6044922 44.1232422,30.0458984 C42.4282552,31.500651 41.5674154,33.5760091 41.5407227,36.2719727 L41.5407227,49.1445313 C41.5674154,51.8271484 42.4282552,53.8958333 44.1232422,55.3505859 C45.7114583,56.7919922 47.6066406,57.5260417 49.8087891,57.5527344 Z M49.8087891,53.3686523 C47.1395182,53.3419596 45.7781901,51.8538411 45.7248047,48.9042969 L45.7248047,36.5322266 C45.7781901,33.609375 47.1395182,32.1212565 49.8087891,32.0678711 C52.4647135,32.1212565 53.8327148,33.609375 53.912793,36.5322266 L53.912793,48.9042969 C53.8327148,51.8538411 52.4647135,53.3419596 49.8087891,53.3686523 Z\"/>"
                                          , "<path id=\"test-d\" d=\"M27.8085937,57.5527344 C30.3844401,57.5260417 32.4931641,56.7052409 34.1347656,55.090332 C35.8030599,53.5154622 36.6505534,51.360026 36.6772461,48.6240234 C36.6772461,47.3961589 36.4169922,46.2216797 35.8964844,45.1005859 C35.3492839,43.9794922 34.4617513,43.0185547 33.2338867,42.2177734 C34.4350586,41.4036458 35.2692057,40.4760742 35.7363281,39.4350586 C36.1367187,38.394043 36.3369141,37.3129883 36.3369141,36.1918945 C36.3102214,33.9763997 35.5494792,32.0545247 34.0546875,30.4262695 C32.4931641,28.7579753 30.3844401,27.9104818 27.7285156,27.8837891 C25.633138,27.9104818 23.8313802,28.6578776 22.3232422,30.1259766 C20.7884115,31.6074219 19.9142253,33.4291992 19.7006836,35.5913086 L23.8847656,35.5913086 C24.1516927,34.3901367 24.6722005,33.5026042 25.4462891,32.9287109 C26.1803385,32.3548177 26.9944661,32.0678711 27.8886719,32.0678711 C29.1298828,32.0945638 30.1442057,32.4882813 30.9316406,33.2490234 C31.7190755,34.0364583 32.1261393,35.0441081 32.152832,36.2719727 C32.152832,37.4864909 31.7591146,38.4741211 30.9716797,39.2348633 C30.1842448,40.0222982 29.0764974,40.4160156 27.6484375,40.4160156 L26.2070312,40.4160156 L26.2070312,44.1196289 L27.96875,44.1196289 C29.2633464,44.1196289 30.3310547,44.5133464 31.171875,45.3007813 C32.0260417,46.1149089 32.4664714,47.2626953 32.4931641,48.7441406 C32.4664714,50.1988932 32.0260417,51.319987 31.171875,52.1074219 C30.3310547,52.9482422 29.2967122,53.3686523 28.0688477,53.3686523 C26.9210612,53.3686523 26.0001628,53.055013 25.3061523,52.4277344 C24.6254883,51.8271484 24.0983073,51.0397135 23.7246094,50.0654297 L19.5405273,50.0654297 C19.9943034,52.4944661 20.9886068,54.3496094 22.5234375,55.6308594 C24.0582682,56.9121094 25.819987,57.5527344 27.8085937,57.5527344 Z M52.1110352,57.3125 L52.1110352,28.1240234 L47.9269531,28.1240234 L43.7829102,31.1469727 L43.7829102,35.5913086 L47.9269531,32.5483398 L47.9269531,57.3125 L52.1110352,57.3125 Z\"/>"
                                         };

    static const QByteArrayList monthList= {"<path fill=\"#E06164\" fill-rule=\"nonzero\" d=\"M30.189375,21.5760333 C32.158125,21.5760333 32.998125,20.1847833 32.998125,18.4522833 L32.998125,11.7322833 L31.475625,11.7322833 L31.475625,18.3079083 C31.475625,19.7254083 30.99,20.2504083 30.01875,20.2504083 C29.38875,20.2504083 28.86375,19.9485333 28.456875,19.2004083 L27.39375,19.9747833 C27.9975,21.0379083 28.89,21.5760333 30.189375,21.5760333 Z M35.7675,21.4054083 L36.594375,18.6491583 L39.954375,18.6491583 L40.768125,21.4054083 L42.3825,21.4054083 L39.18,11.7322833 L37.42125,11.7322833 L34.21875,21.4054083 L35.7675,21.4054083 Z M39.586875,17.4547833 L36.94875,17.4547833 L37.3425,16.1554083 C37.6575,15.1054083 37.959375,14.0422833 38.235,12.9397833 L38.300625,12.9397833 C38.589375,14.0291583 38.878125,15.1054083 39.20625,16.1554083 L39.586875,17.4547833 Z M45.099375,21.4054083 L45.099375,16.8510333 C45.099375,15.8010333 44.98125,14.6854083 44.9025,13.6879083 L44.968125,13.6879083 L45.97875,15.7091583 L49.18125,21.4054083 L50.743125,21.4054083 L50.743125,11.7322833 L49.299375,11.7322833 L49.299375,16.2472833 C49.299375,17.2972833 49.4175,18.4654083 49.49625,19.4629083 L49.430625,19.4629083 L48.42,17.4154083 L45.2175,11.7322833 L43.655625,11.7322833 L43.655625,21.4054083 L45.099375,21.4054083 Z\"/>\n"
                                            , "<path fill=\"#E06164\" fill-rule=\"nonzero\" d=\"M29.795625,21.4054083 L29.795625,17.2447833 L33.365625,17.2447833 L33.365625,15.9716583 L29.795625,15.9716583 L29.795625,13.0185333 L33.995625,13.0185333 L33.995625,11.7322833 L28.273125,11.7322833 L28.273125,21.4054083 L29.795625,21.4054083 Z M41.555625,21.4054083 L41.555625,20.1191583 L37.224375,20.1191583 L37.224375,16.9954083 L40.768125,16.9954083 L40.768125,15.7091583 L37.224375,15.7091583 L37.224375,13.0185333 L41.41125,13.0185333 L41.41125,11.7322833 L35.701875,11.7322833 L35.701875,21.4054083 L41.555625,21.4054083 Z M46.805625,21.4054083 C48.945,21.4054083 50.506875,20.4866583 50.506875,18.5704083 C50.506875,17.2579083 49.70625,16.5097833 48.60375,16.2735333 L48.60375,16.2079083 C49.47,15.9191583 49.981875,15.0397833 49.981875,14.1210333 C49.981875,12.3754083 48.55125,11.7322833 46.569375,11.7322833 L43.576875,11.7322833 L43.576875,21.4054083 L46.805625,21.4054083 Z M46.39875,15.7747833 L45.099375,15.7747833 L45.099375,12.9266583 L46.438125,12.9266583 C47.803125,12.9266583 48.485625,13.3072833 48.485625,14.3179083 C48.485625,15.2235333 47.86875,15.7747833 46.39875,15.7747833 Z M46.635,20.2110333 L45.099375,20.2110333 L45.099375,16.9166583 L46.635,16.9166583 C48.170625,16.9166583 49.010625,17.4022833 49.010625,18.4916583 C49.010625,19.6729083 48.144375,20.2110333 46.635,20.2110333 Z\"/>\n"
                                            , "<text fill=\"#E06164\" font-family=\"SourceHanSansSC-Medium, Source Han Sans SC\" font-size=\"13.125\" font-weight=\"400\">\n"
                                            "<tspan x=\"27\" y=\"21.405\">MAR</tspan>\n"
                                            "</text>\n"
                                            , "<path fill=\"#E06164\" fill-rule=\"nonzero\" d=\"M27.54875,19.3125 L28.375625,16.55625 L31.735625,16.55625 L32.549375,19.3125 L34.16375,19.3125 L30.96125,9.639375 L29.2025,9.639375 L26,19.3125 L27.54875,19.3125 Z M31.368125,15.361875 L28.73,15.361875 L29.12375,14.0625 C29.43875,13.0125 29.740625,11.949375 30.01625,10.846875 L30.081875,10.846875 C30.370625,11.93625 30.659375,13.0125 30.9875,14.0625 L31.368125,15.361875 Z M36.959375,19.3125 L36.959375,15.650625 L38.41625,15.650625 C40.51625,15.650625 42.051875,14.679375 42.051875,12.56625 C42.051875,10.3875 40.51625,9.639375 38.36375,9.639375 L35.436875,9.639375 L35.436875,19.3125 L36.959375,19.3125 Z M38.271875,14.43 L36.959375,14.43 L36.959375,10.873125 L38.219375,10.873125 C39.755,10.873125 40.555625,11.293125 40.555625,12.56625 C40.555625,13.839375 39.820625,14.43 38.271875,14.43 Z M45.45125,19.3125 L45.45125,15.414375 L47.013125,15.414375 L49.205,19.3125 L50.924375,19.3125 L48.561875,15.2175 C49.7825,14.836875 50.583125,13.944375 50.583125,12.448125 C50.583125,10.36125 49.086875,9.639375 47.091875,9.639375 L43.92875,9.639375 L43.92875,19.3125 L45.45125,19.3125 Z M46.908125,14.19375 L45.45125,14.19375 L45.45125,10.873125 L46.908125,10.873125 C48.3125,10.873125 49.07375,11.28 49.07375,12.448125 C49.07375,13.61625 48.3125,14.19375 46.908125,14.19375 Z\"/>\n"
                                            , "<path fill=\"#E06164\" fill-rule=\"nonzero\" d=\"M28.638125,19.3125 L28.638125,14.535 C28.638125,13.66875 28.52,12.421875 28.454375,11.5425 L28.506875,11.5425 L29.268125,13.77375 L30.96125,18.380625 L31.90625,18.380625 L33.58625,13.77375 L34.360625,11.5425 L34.413125,11.5425 C34.334375,12.421875 34.21625,13.66875 34.21625,14.535 L34.21625,19.3125 L35.63375,19.3125 L35.63375,9.639375 L33.875,9.639375 L32.129375,14.535 C31.90625,15.178125 31.7225,15.834375 31.499375,16.4775 L31.43375,16.4775 C31.210625,15.834375 31.01375,15.178125 30.790625,14.535 L29.01875,9.639375 L27.273125,9.639375 L27.273125,19.3125 L28.638125,19.3125 Z M38.4425,19.3125 L39.269375,16.55625 L42.629375,16.55625 L43.443125,19.3125 L45.0575,19.3125 L41.855,9.639375 L40.09625,9.639375 L36.89375,19.3125 L38.4425,19.3125 Z M42.261875,15.361875 L39.62375,15.361875 L40.0175,14.0625 C40.3325,13.0125 40.634375,11.949375 40.91,10.846875 L40.975625,10.846875 C41.264375,11.93625 41.553125,13.0125 41.88125,14.0625 L42.261875,15.361875 Z M49.44125,19.3125 L49.44125,15.650625 L52.355,9.639375 L50.766875,9.639375 L49.638125,12.211875 C49.349375,12.93375 49.034375,13.603125 48.719375,14.338125 L48.666875,14.338125 C48.33875,13.603125 48.063125,12.93375 47.76125,12.211875 L46.645625,9.639375 L45.018125,9.639375 L47.91875,15.650625 L47.91875,19.3125 L49.44125,19.3125 Z\"/>\n"
                                            , "<path fill=\"#E06164\" fill-rule=\"nonzero\" d=\"M29.189375,19.483125 C31.158125,19.483125 31.998125,18.091875 31.998125,16.359375 L31.998125,9.639375 L30.475625,9.639375 L30.475625,16.215 C30.475625,17.6325 29.99,18.1575 29.01875,18.1575 C28.38875,18.1575 27.86375,17.855625 27.456875,17.1075 L26.39375,17.881875 C26.9975,18.945 27.89,19.483125 29.189375,19.483125 Z M38.035625,19.483125 C40.175,19.483125 41.61875,18.315 41.61875,15.165 L41.61875,9.639375 L40.14875,9.639375 L40.14875,15.27 C40.14875,17.44875 39.25625,18.1575 38.035625,18.1575 C36.828125,18.1575 35.961875,17.44875 35.961875,15.27 L35.961875,9.639375 L34.439375,9.639375 L34.439375,15.165 C34.439375,18.315 35.89625,19.483125 38.035625,19.483125 Z M45.55625,19.3125 L45.55625,14.758125 C45.55625,13.708125 45.438125,12.5925 45.359375,11.595 L45.425,11.595 L46.435625,13.61625 L49.638125,19.3125 L51.2,19.3125 L51.2,9.639375 L49.75625,9.639375 L49.75625,14.154375 C49.75625,15.204375 49.874375,16.3725 49.953125,17.37 L49.8875,17.37 L48.876875,15.3225 L45.674375,9.639375 L44.1125,9.639375 L44.1125,19.3125 L45.55625,19.3125 Z\"/>\n"
                                            , "<path fill=\"#E06164\" fill-rule=\"nonzero\" d=\"M29.189375,19.483125 C31.158125,19.483125 31.998125,18.091875 31.998125,16.359375 L31.998125,9.639375 L30.475625,9.639375 L30.475625,16.215 C30.475625,17.6325 29.99,18.1575 29.01875,18.1575 C28.38875,18.1575 27.86375,17.855625 27.456875,17.1075 L26.39375,17.881875 C26.9975,18.945 27.89,19.483125 29.189375,19.483125 Z M38.035625,19.483125 C40.175,19.483125 41.61875,18.315 41.61875,15.165 L41.61875,9.639375 L40.14875,9.639375 L40.14875,15.27 C40.14875,17.44875 39.25625,18.1575 38.035625,18.1575 C36.828125,18.1575 35.961875,17.44875 35.961875,15.27 L35.961875,9.639375 L34.439375,9.639375 L34.439375,15.165 C34.439375,18.315 35.89625,19.483125 38.035625,19.483125 Z M49.73,19.3125 L49.73,18.02625 L45.635,18.02625 L45.635,9.639375 L44.1125,9.639375 L44.1125,19.3125 L49.73,19.3125 Z\"/>\n"
                                            , "<path fill=\"#E06164\" fill-rule=\"nonzero\" d=\"M27.54875,19.3125 L28.375625,16.55625 L31.735625,16.55625 L32.549375,19.3125 L34.16375,19.3125 L30.96125,9.639375 L29.2025,9.639375 L26,19.3125 L27.54875,19.3125 Z M31.368125,15.361875 L28.73,15.361875 L29.12375,14.0625 C29.43875,13.0125 29.740625,11.949375 30.01625,10.846875 L30.081875,10.846875 C30.370625,11.93625 30.659375,13.0125 30.9875,14.0625 L31.368125,15.361875 Z M38.980625,19.483125 C41.12,19.483125 42.56375,18.315 42.56375,15.165 L42.56375,9.639375 L41.09375,9.639375 L41.09375,15.27 C41.09375,17.44875 40.20125,18.1575 38.980625,18.1575 C37.773125,18.1575 36.906875,17.44875 36.906875,15.27 L36.906875,9.639375 L35.384375,9.639375 L35.384375,15.165 C35.384375,18.315 36.84125,19.483125 38.980625,19.483125 Z M48.995,19.483125 C50.320625,19.483125 51.41,18.9975 52.053125,18.354375 L52.053125,14.1675 L48.75875,14.1675 L48.75875,15.414375 L50.675,15.414375 L50.675,17.685 C50.33375,17.986875 49.743125,18.1575 49.1525,18.1575 C47.1575,18.1575 46.094375,16.753125 46.094375,14.45625 C46.094375,12.185625 47.28875,10.794375 49.086875,10.794375 C50.01875,10.794375 50.609375,11.175 51.095,11.660625 L51.90875,10.689375 C51.318125,10.0725 50.399375,9.46875 49.034375,9.46875 C46.475,9.46875 44.5325,11.35875 44.5325,14.50875 C44.5325,17.671875 46.4225,19.483125 48.995,19.483125 Z\"/>\n"
                                            , "<path fill=\"#E06164\" fill-rule=\"nonzero\" d=\"M30.029375,19.483125 C32.1425,19.483125 33.42875,18.223125 33.42875,16.674375 C33.42875,15.256875 32.601875,14.548125 31.46,14.0625 L30.134375,13.498125 C29.346875,13.183125 28.5725,12.868125 28.5725,12.028125 C28.5725,11.266875 29.215625,10.794375 30.2,10.794375 C31.053125,10.794375 31.7225,11.1225 32.32625,11.660625 L33.126875,10.689375 C32.391875,9.94125 31.32875,9.46875 30.2,9.46875 C28.3625,9.46875 27.02375,10.610625 27.02375,12.133125 C27.02375,13.5375 28.060625,14.2725 28.9925,14.66625 L30.33125,15.24375 C31.22375,15.6375 31.88,15.913125 31.88,16.7925 C31.88,17.60625 31.22375,18.1575 30.055625,18.1575 C29.12375,18.1575 28.17875,17.71125 27.483125,17.015625 L26.590625,18.065625 C27.47,18.958125 28.70375,19.483125 30.029375,19.483125 Z M41.106875,19.3125 L41.106875,18.02625 L36.775625,18.02625 L36.775625,14.9025 L40.319375,14.9025 L40.319375,13.61625 L36.775625,13.61625 L36.775625,10.925625 L40.9625,10.925625 L40.9625,9.639375 L35.253125,9.639375 L35.253125,19.3125 L41.106875,19.3125 Z M44.650625,19.3125 L44.650625,15.650625 L46.1075,15.650625 C48.2075,15.650625 49.743125,14.679375 49.743125,12.56625 C49.743125,10.3875 48.2075,9.639375 46.055,9.639375 L43.128125,9.639375 L43.128125,19.3125 L44.650625,19.3125 Z M45.963125,14.43 L44.650625,14.43 L44.650625,10.873125 L45.910625,10.873125 C47.44625,10.873125 48.246875,11.293125 48.246875,12.56625 C48.246875,13.839375 47.511875,14.43 45.963125,14.43 Z\"/>\n"
                                            , "<path fill=\"#E06164\" fill-rule=\"nonzero\" d=\"M30.948125,19.483125 C33.42875,19.483125 35.16125,17.55375 35.16125,14.443125 C35.16125,11.3325 33.42875,9.46875 30.948125,9.46875 C28.4675,9.46875 26.748125,11.3325 26.748125,14.443125 C26.748125,17.55375 28.4675,19.483125 30.948125,19.483125 Z M30.948125,18.1575 C29.346875,18.1575 28.31,16.700625 28.31,14.443125 C28.31,12.1725 29.346875,10.794375 30.948125,10.794375 C32.549375,10.794375 33.599375,12.1725 33.599375,14.443125 C33.599375,16.700625 32.549375,18.1575 30.948125,18.1575 Z M40.93625,19.483125 C42.183125,19.483125 43.1675,18.984375 43.941875,18.091875 L43.128125,17.13375 C42.550625,17.76375 41.88125,18.1575 40.98875,18.1575 C39.2825,18.1575 38.20625,16.753125 38.20625,14.45625 C38.20625,12.185625 39.36125,10.794375 41.028125,10.794375 C41.815625,10.794375 42.419375,11.14875 42.918125,11.660625 L43.745,10.689375 C43.154375,10.04625 42.209375,9.46875 41.001875,9.46875 C38.560625,9.46875 36.644375,11.35875 36.644375,14.50875 C36.644375,17.671875 38.508125,19.483125 40.93625,19.483125 Z M49.139375,19.3125 L49.139375,10.925625 L51.9875,10.925625 L51.9875,9.639375 L44.781875,9.639375 L44.781875,10.925625 L47.616875,10.925625 L47.616875,19.3125 L49.139375,19.3125 Z\"/>\n"
                                            , "<path fill=\"#E06164\" fill-rule=\"nonzero\" d=\"M28.716875,19.3125 L28.716875,14.758125 C28.716875,13.708125 28.59875,12.5925 28.52,11.595 L28.585625,11.595 L29.59625,13.61625 L32.79875,19.3125 L34.360625,19.3125 L34.360625,9.639375 L32.916875,9.639375 L32.916875,14.154375 C32.916875,15.204375 33.035,16.3725 33.11375,17.37 L33.048125,17.37 L32.0375,15.3225 L28.835,9.639375 L27.273125,9.639375 L27.273125,19.3125 L28.716875,19.3125 Z M40.581875,19.483125 C43.0625,19.483125 44.795,17.55375 44.795,14.443125 C44.795,11.3325 43.0625,9.46875 40.581875,9.46875 C38.10125,9.46875 36.381875,11.3325 36.381875,14.443125 C36.381875,17.55375 38.10125,19.483125 40.581875,19.483125 Z M40.581875,18.1575 C38.980625,18.1575 37.94375,16.700625 37.94375,14.443125 C37.94375,12.1725 38.980625,10.794375 40.581875,10.794375 C42.183125,10.794375 43.233125,12.1725 43.233125,14.443125 C43.233125,16.700625 42.183125,18.1575 40.581875,18.1575 Z M50.33375,19.3125 L53.3525,9.639375 L51.80375,9.639375 L50.373125,14.653125 C50.058125,15.755625 49.835,16.700625 49.49375,17.81625 L49.428125,17.81625 C49.1,16.700625 48.876875,15.755625 48.54875,14.653125 L47.118125,9.639375 L45.50375,9.639375 L48.535625,19.3125 L50.33375,19.3125 Z\"/>\n"
                                            , "<path fill=\"#E06164\" fill-rule=\"nonzero\" d=\"M29.85875,19.3125 C32.74625,19.3125 34.439375,17.58 34.439375,14.443125 C34.439375,11.293125 32.74625,9.639375 29.78,9.639375 L27.273125,9.639375 L27.273125,19.3125 L29.85875,19.3125 Z M29.675,18.065625 L28.795625,18.065625 L28.795625,10.88625 L29.675,10.88625 C31.74875,10.88625 32.864375,12.028125 32.864375,14.443125 C32.864375,16.845 31.74875,18.065625 29.675,18.065625 Z M42.30125,19.3125 L42.30125,18.02625 L37.97,18.02625 L37.97,14.9025 L41.51375,14.9025 L41.51375,13.61625 L37.97,13.61625 L37.97,10.925625 L42.156875,10.925625 L42.156875,9.639375 L36.4475,9.639375 L36.4475,19.3125 L42.30125,19.3125 Z M48.089375,19.483125 C49.33625,19.483125 50.320625,18.984375 51.095,18.091875 L50.28125,17.13375 C49.70375,17.76375 49.034375,18.1575 48.141875,18.1575 C46.435625,18.1575 45.359375,16.753125 45.359375,14.45625 C45.359375,12.185625 46.514375,10.794375 48.18125,10.794375 C48.96875,10.794375 49.5725,11.14875 50.07125,11.660625 L50.898125,10.689375 C50.3075,10.04625 49.3625,9.46875 48.155,9.46875 C45.71375,9.46875 43.7975,11.35875 43.7975,14.50875 C43.7975,17.671875 45.66125,19.483125 48.089375,19.483125 Z\"/>\n"
                                           };

    static const QByteArrayList weekList= {"<path fill=\"#2D394F\" fill-rule=\"nonzero\" d=\"M35.5075,71.4054083 L35.5075,68.6754083 C35.5075,68.1804083 35.44,67.4679083 35.4025,66.9654083 L35.4325,66.9654083 L35.8675,68.2404083 L36.835,70.8729083 L37.375,70.8729083 L38.335,68.2404083 L38.7775,66.9654083 L38.8075,66.9654083 C38.7625,67.4679083 38.695,68.1804083 38.695,68.6754083 L38.695,71.4054083 L39.505,71.4054083 L39.505,65.8779083 L38.5,65.8779083 L37.5025,68.6754083 L37.1425,69.7854083 L37.1425,69.7854083 L37.105,69.7854083 L36.7375,68.6754083 L36.7375,68.6754083 L35.725,65.8779083 L34.7275,65.8779083 L34.7275,71.4054083 L35.5075,71.4054083 Z M43.0525,71.5029083 C44.47,71.5029083 45.46,70.4004083 45.46,68.6229083 C45.46,66.8454083 44.47,65.7804083 43.0525,65.7804083 C41.635,65.7804083 40.6525,66.8454083 40.6525,68.6229083 C40.6525,70.4004083 41.635,71.5029083 43.0525,71.5029083 Z M43.0525,70.7454083 C42.1375,70.7454083 41.545,69.9129083 41.545,68.6229083 C41.545,67.3254083 42.1375,66.5379083 43.0525,66.5379083 C43.9675,66.5379083 44.5675,67.3254083 44.5675,68.6229083 C44.5675,69.9129083 43.9675,70.7454083 43.0525,70.7454083 Z M47.4325,71.4054083 L47.4325,68.8029083 C47.4325,68.2029083 47.365,67.5654083 47.32,66.9954083 L47.3575,66.9954083 L47.935,68.1504083 L49.765,71.4054083 L50.6575,71.4054083 L50.6575,65.8779083 L49.8325,65.8779083 L49.8325,68.4579083 C49.8325,69.0579083 49.9,69.7254083 49.945,70.2954083 L49.9075,70.2954083 L49.33,69.1254083 L47.5,65.8779083 L46.6075,65.8779083 L46.6075,71.4054083 L47.4325,71.4054083 Z\"/>\n"
                                           , "<path fill=\"#2D394F\" fill-rule=\"nonzero\" d=\"M33.1975,70.9100019 L33.1975,66.1175019 L34.825,66.1175019 L34.825,65.3825019 L30.7075,65.3825019 L30.7075,66.1175019 L32.3275,66.1175019 L32.3275,70.9100019 L33.1975,70.9100019 Z M37.8025,71.0075019 C39.025,71.0075019 39.85,70.3400019 39.85,68.5400019 L39.85,65.3825019 L39.01,65.3825019 L39.01,68.6000019 C39.01,69.8450019 38.5,70.2500019 37.8025,70.2500019 C37.1125,70.2500019 36.6175,69.8450019 36.6175,68.6000019 L36.6175,65.3825019 L35.7475,65.3825019 L35.7475,68.5400019 C35.7475,70.3400019 36.58,71.0075019 37.8025,71.0075019 Z M44.62,70.9100019 L44.62,70.1750019 L42.145,70.1750019 L42.145,68.3900019 L44.17,68.3900019 L44.17,67.6550019 L42.145,67.6550019 L42.145,66.1175019 L44.5375,66.1175019 L44.5375,65.3825019 L41.275,65.3825019 L41.275,70.9100019 L44.62,70.9100019 Z M47.35,71.0075019 C48.5575,71.0075019 49.2925,70.2875019 49.2925,69.4025019 C49.2925,68.5925019 48.82,68.1875019 48.1675,67.9100019 L47.41,67.5875019 C46.96,67.4075019 46.5175,67.2275019 46.5175,66.7475019 C46.5175,66.3125019 46.885,66.0425019 47.4475,66.0425019 C47.935,66.0425019 48.3175,66.2300019 48.6625,66.5375019 L49.12,65.9825019 C48.7,65.5550019 48.0925,65.2850019 47.4475,65.2850019 C46.3975,65.2850019 45.6325,65.9375019 45.6325,66.8075019 C45.6325,67.6100019 46.225,68.0300019 46.7575,68.2550019 L47.5225,68.5850019 C48.0325,68.8100019 48.4075,68.9675019 48.4075,69.4700019 C48.4075,69.9350019 48.0325,70.2500019 47.365,70.2500019 C46.8325,70.2500019 46.2925,69.9950019 45.895,69.5975019 L45.385,70.1975019 C45.8875,70.7075019 46.5925,71.0075019 47.35,71.0075019 Z\"/>\n"
                                           , "<path fill=\"#2D394F\" fill-rule=\"nonzero\" d=\"M36.3475,71.4054083 L37.075,68.3379083 C37.165,67.9029083 37.255,67.4904083 37.3375,67.0704083 L37.3675,67.0704083 C37.4425,67.4904083 37.525,67.9029083 37.6225,68.3379083 L38.365,71.4054083 L39.4375,71.4054083 L40.525,65.8779083 L39.6925,65.8779083 L39.1675,68.7579083 C39.0775,69.3354083 38.98,69.9279083 38.89,70.5279083 L38.8525,70.5279083 C38.725,69.9279083 38.605,69.3354083 38.4775,68.7579083 L37.765,65.8779083 L36.9925,65.8779083 L36.28,68.7579083 C36.1525,69.3429083 36.025,69.9354083 35.905,70.5279083 L35.875,70.5279083 C35.7775,69.9354083 35.68,69.3429083 35.5825,68.7579083 L35.065,65.8779083 L34.1725,65.8779083 L35.2975,71.4054083 L36.3475,71.4054083 Z M44.7775,71.4054083 L44.7775,70.6704083 L42.3025,70.6704083 L42.3025,68.8854083 L44.3275,68.8854083 L44.3275,68.1504083 L42.3025,68.1504083 L42.3025,66.6129083 L44.695,66.6129083 L44.695,65.8779083 L41.4325,65.8779083 L41.4325,71.4054083 L44.7775,71.4054083 Z M47.41,71.4054083 C49.06,71.4054083 50.0275,70.4154083 50.0275,68.6229083 C50.0275,66.8229083 49.06,65.8779083 47.365,65.8779083 L45.9325,65.8779083 L45.9325,71.4054083 L47.41,71.4054083 Z M47.305,70.6929083 L46.8025,70.6929083 L46.8025,66.5904083 L47.305,66.5904083 C48.49,66.5904083 49.1275,67.2429083 49.1275,68.6229083 C49.1275,69.9954083 48.49,70.6929083 47.305,70.6929083 Z\"/>\n"
                                           , "<path fill=\"#2D394F\" fill-rule=\"nonzero\" d=\"M32.4325,70.8024882 L32.4325,66.0099882 L34.06,66.0099882 L34.06,65.2749882 L29.9425,65.2749882 L29.9425,66.0099882 L31.5625,66.0099882 L31.5625,70.8024882 L32.4325,70.8024882 Z M35.8825,70.8024882 L35.8825,68.2899882 L38.245,68.2899882 L38.245,70.8024882 L39.115,70.8024882 L39.115,65.2749882 L38.245,65.2749882 L38.245,67.5324882 L35.8825,67.5324882 L35.8825,65.2749882 L35.0125,65.2749882 L35.0125,70.8024882 L35.8825,70.8024882 Z M42.5875,70.8999882 C43.81,70.8999882 44.635,70.2324882 44.635,68.4324882 L44.635,65.2749882 L43.795,65.2749882 L43.795,68.4924882 C43.795,69.7374882 43.285,70.1424882 42.5875,70.1424882 C41.8975,70.1424882 41.4025,69.7374882 41.4025,68.4924882 L41.4025,65.2749882 L40.5325,65.2749882 L40.5325,68.4324882 C40.5325,70.2324882 41.365,70.8999882 42.5875,70.8999882 Z M46.93,70.8024882 L46.93,68.5749882 L47.8225,68.5749882 L49.075,70.8024882 L50.0575,70.8024882 L48.7075,68.4624882 C49.405,68.2449882 49.8625,67.7349882 49.8625,66.8799882 C49.8625,65.6874882 49.0075,65.2749882 47.8675,65.2749882 L46.06,65.2749882 L46.06,70.8024882 L46.93,70.8024882 Z M47.7625,67.8774882 L46.93,67.8774882 L46.93,65.9799882 L47.7625,65.9799882 C48.565,65.9799882 49,66.2124882 49,66.8799882 C49,67.5474882 48.565,67.8774882 47.7625,67.8774882 Z\"/>\n"
                                           , "<path fill=\"#2D394F\" fill-rule=\"nonzero\" d=\"M35.5975,71.4054083 L35.5975,69.0279083 L37.6375,69.0279083 L37.6375,68.3004083 L35.5975,68.3004083 L35.5975,66.6129083 L37.9975,66.6129083 L37.9975,65.8779083 L34.7275,65.8779083 L34.7275,71.4054083 L35.5975,71.4054083 Z M39.8425,71.4054083 L39.8425,69.1779083 L40.735,69.1779083 L41.9875,71.4054083 L42.97,71.4054083 L41.62,69.0654083 C42.3175,68.8479083 42.775,68.3379083 42.775,67.4829083 C42.775,66.2904083 41.92,65.8779083 40.78,65.8779083 L38.9725,65.8779083 L38.9725,71.4054083 L39.8425,71.4054083 Z M40.675,68.4804083 L39.8425,68.4804083 L39.8425,66.5829083 L40.675,66.5829083 C41.4775,66.5829083 41.9125,66.8154083 41.9125,67.4829083 C41.9125,68.1504083 41.4775,68.4804083 40.675,68.4804083 Z M44.7625,71.4054083 L44.7625,65.8779083 L43.8925,65.8779083 L43.8925,71.4054083 L44.7625,71.4054083 Z\"/>\n"
                                           , "<path fill=\"#2D394F\" fill-rule=\"nonzero\" d=\"M36.3025,71.5029083 C37.51,71.5029083 38.245,70.7829083 38.245,69.8979083 C38.245,69.0879083 37.7725,68.6829083 37.12,68.4054083 L36.3625,68.0829083 C35.9125,67.9029083 35.47,67.7229083 35.47,67.2429083 C35.47,66.8079083 35.8375,66.5379083 36.4,66.5379083 C36.8875,66.5379083 37.27,66.7254083 37.615,67.0329083 L38.0725,66.4779083 C37.6525,66.0504083 37.045,65.7804083 36.4,65.7804083 C35.35,65.7804083 34.585,66.4329083 34.585,67.3029083 C34.585,68.1054083 35.1775,68.5254083 35.71,68.7504083 L36.475,69.0804083 C36.985,69.3054083 37.36,69.4629083 37.36,69.9654083 C37.36,70.4304083 36.985,70.7454083 36.3175,70.7454083 C35.785,70.7454083 35.245,70.4904083 34.8475,70.0929083 L34.3375,70.6929083 C34.84,71.2029083 35.545,71.5029083 36.3025,71.5029083 Z M39.445,71.4054083 L39.9175,69.8304083 L41.8375,69.8304083 L42.3025,71.4054083 L43.225,71.4054083 L41.395,65.8779083 L40.39,65.8779083 L38.56,71.4054083 L39.445,71.4054083 Z M41.6275,69.1479083 L40.12,69.1479083 L40.345,68.4054083 C40.525,67.8054083 40.6975,67.1979083 40.855,66.5679083 L40.8925,66.5679083 C41.0575,67.1904083 41.2225,67.8054083 41.41,68.4054083 L41.6275,69.1479083 Z M45.9475,71.4054083 L45.9475,66.6129083 L47.575,66.6129083 L47.575,65.8779083 L43.4575,65.8779083 L43.4575,66.6129083 L45.0775,66.6129083 L45.0775,71.4054083 L45.9475,71.4054083 Z\"/>\n"
                                           , "<path fill=\"#2D394F\" fill-rule=\"nonzero\" d=\"M36.3025,71.5029083 C37.51,71.5029083 38.245,70.7829083 38.245,69.8979083 C38.245,69.0879083 37.7725,68.6829083 37.12,68.4054083 L36.3625,68.0829083 C35.9125,67.9029083 35.47,67.7229083 35.47,67.2429083 C35.47,66.8079083 35.8375,66.5379083 36.4,66.5379083 C36.8875,66.5379083 37.27,66.7254083 37.615,67.0329083 L38.0725,66.4779083 C37.6525,66.0504083 37.045,65.7804083 36.4,65.7804083 C35.35,65.7804083 34.585,66.4329083 34.585,67.3029083 C34.585,68.1054083 35.1775,68.5254083 35.71,68.7504083 L36.475,69.0804083 C36.985,69.3054083 37.36,69.4629083 37.36,69.9654083 C37.36,70.4304083 36.985,70.7454083 36.3175,70.7454083 C35.785,70.7454083 35.245,70.4904083 34.8475,70.0929083 L34.3375,70.6929083 C34.84,71.2029083 35.545,71.5029083 36.3025,71.5029083 Z M41.3125,71.5029083 C42.535,71.5029083 43.36,70.8354083 43.36,69.0354083 L43.36,65.8779083 L42.52,65.8779083 L42.52,69.0954083 C42.52,70.3404083 42.01,70.7454083 41.3125,70.7454083 C40.6225,70.7454083 40.1275,70.3404083 40.1275,69.0954083 L40.1275,65.8779083 L39.2575,65.8779083 L39.2575,69.0354083 C39.2575,70.8354083 40.09,71.5029083 41.3125,71.5029083 Z M45.61,71.4054083 L45.61,68.8029083 C45.61,68.2029083 45.5425,67.5654083 45.4975,66.9954083 L45.535,66.9954083 L46.1125,68.1504083 L47.9425,71.4054083 L48.835,71.4054083 L48.835,65.8779083 L48.01,65.8779083 L48.01,68.4579083 C48.01,69.0579083 48.0775,69.7254083 48.1225,70.2954083 L48.085,70.2954083 L47.5075,69.1254083 L45.6775,65.8779083 L44.785,65.8779083 L44.785,71.4054083 L45.61,71.4054083 Z\"/>\n"
                                          };


    if (!QFile(fileName).exists()) {
        // create svg
        QFile file(fileName);
        if (!file.open(QIODevice::WriteOnly | QIODevice::Truncate))
            return false;

        file.write(QByteArray("<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"96\" height=\"96\" viewBox=\"0 0 96 96\">\n"
                              "  <defs>\n"
                              "    <filter id=\"test-a\" width=\"131.2%\" height=\"132.5%\" x=\"-15%\" y=\"-15%\" filterUnits=\"objectBoundingBox\">\n"
                              "      <feOffset dy=\"2\" in=\"SourceAlpha\" result=\"shadowOffsetOuter1\"/>\n"
                              "      <feGaussianBlur in=\"shadowOffsetOuter1\" result=\"shadowBlurOuter1\" stdDeviation=\"2\"/>\n"
                              "      <feColorMatrix in=\"shadowBlurOuter1\" result=\"shadowMatrixOuter1\" values=\"0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.2 0\"/>\n"
                              "      <feMerge>\n"
                              "        <feMergeNode in=\"shadowMatrixOuter1\"/>\n"
                              "        <feMergeNode in=\"SourceGraphic\"/>\n"
                              "      </feMerge>\n"
                              "    </filter>\n"
                              "    <radialGradient id=\"test-b\" cx=\"54.324%\" cy=\"55.779%\" r=\"61.969%\" fx=\"54.324%\" fy=\"55.779%\" gradientTransform=\"matrix(-.81842 -.50567 .72673 -.91085 .582 1.34)\">\n"
                              "      <stop offset=\"0%\"/>\n"
                              "      <stop offset=\"100%\" stop-opacity=\".148\"/>\n"
                              "    </radialGradient>\n"));
        // 日期
        file.write(dayList.at(QDate::currentDate().day() - 1));
        file.write(QByteArray("<filter id=\"test-c\" width=\"144.4%\" height=\"147.2%\" x=\"-22.2%\" y=\"-16.9%\" filterUnits=\"objectBoundingBox\">\n"
                              "      <feOffset dy=\"2\" in=\"SourceAlpha\" result=\"shadowOffsetOuter1\"/>\n"
                              "      <feGaussianBlur in=\"shadowOffsetOuter1\" result=\"shadowBlurOuter1\" stdDeviation=\"2\"/>\n"
                              "      <feColorMatrix in=\"shadowBlurOuter1\" values=\"0 0 0 0 0.000854821203   0 0 0 0 0.168099661   0 0 0 0 0.309386322  0 0 0 0.2 0\"/>\n"
                              "    </filter>\n"
                              "    <linearGradient id=\"test-g\" x1=\"69.76%\" x2=\"56.983%\" y1=\"71.097%\" y2=\"57.701%\">\n"
                              "      <stop offset=\"0%\" stop-color=\"#C6C6C6\"/>\n"
                              "      <stop offset=\"53.052%\" stop-color=\"#E7E7E7\"/>\n"
                              "      <stop offset=\"100%\" stop-color=\"#F4F4F4\"/>\n"
                              "    </linearGradient>\n"));
        // 右下角
        file.write(QByteArray("<path id=\"test-f\" d=\"M80,54 C80,54 78.7993866,60.7153889 77.4375,62.90625 C75.28125,66.375 73.6875,65.0625 70.6875,68.71875 C67.6875,72.375 69.75,73.03125 65.25,76.875 C62.8384357,78.9348779 57,80 57,80 C75.65625,80 80,64.03125 80,54 Z\"/>\n"
                              "    <filter id=\"test-e\" width=\"117.4%\" height=\"115.4%\" x=\"-8.7%\" y=\"-3.8%\" filterUnits=\"objectBoundingBox\">\n"
                              "      <feOffset dy=\"1\" in=\"SourceAlpha\" result=\"shadowOffsetOuter1\"/>\n"
                              "      <feGaussianBlur in=\"shadowOffsetOuter1\" result=\"shadowBlurOuter1\" stdDeviation=\".5\"/>\n"
                              "      <feColorMatrix in=\"shadowBlurOuter1\" values=\"0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.05 0\"/>\n"
                              "    </filter>\n"
                              "  </defs>\n"));

        // 背景
        file.write(QByteArray("<g fill=\"none\" fill-rule=\"evenodd\" filter=\"url(#test-a)\" transform=\"rotate(-8 101.203 -13.203)\">\n"
                              "    <rect width=\"80\" height=\"80\" fill=\"#FFF\" rx=\"14.625\"/>\n"
                              "    <path fill=\"url(#test-b)\" fill-opacity=\".39\" d=\"M79.9991218,55.0050227 L80,65.375 C80,73.4521645 73.4521645,80 65.375,80 L58,80 C65.5166667,79.0808824 70.8027778,76.1703431 73.8583333,71.2683824 C76.9130282,66.3678024 78.9599577,60.9466825 79.9991218,55.0050227 Z\"/>\n"));
        // 月份
        file.write(monthList.at(QDate::currentDate().month() - 1));
        // 日期
        file.write(QByteArray("<g fill-rule=\"nonzero\">\n"
                              "      <use fill=\"#000\" filter=\"url(#test-c)\" xlink:href=\"#test-d\"/>\n"
                              "      <use fill=\"#2D394F\" xlink:href=\"#test-d\"/>\n"
                              "      <use fill=\"#2D394F\" xlink:href=\"#test-d\"/>\n"
                              "    </g>\n"));
        // 星期
        file.write(weekList.at(QDate::currentDate().dayOfWeek() - 1));

        // 右下角
        file.write(QByteArray("<use fill=\"#000\" filter=\"url(#test-e)\" xlink:href=\"#test-f\"/>\n"
                              "<use fill=\"url(#test-g)\" xlink:href=\"#test-f\"/>\n"
                              "</g>\n"
                              "</svg>\n"));

        file.close();
    }

    return true;
}

int perfectIconSize(const int size)
{
    for (int i = 0; i < DLauncher::APP_ICON_SIZE_LIST.size(); ++i)
        if (size <= DLauncher::APP_ICON_SIZE_LIST.at(i))
            return DLauncher::APP_ICON_SIZE_LIST.at(i);

    return 256;
}

/**
 * @brief getThemeIcon 从系统主题或者缓存中获取应用图标
 * @param pixmap 应用图标对象
 * @param itemInfo 应用程序信息
 * @param size 应用程序大小
 * @param reObtain 是否重新获取标识
 * @return 返回是否获取到应用图标状态
 */
bool getThemeIcon(QPixmap &pixmap, const ItemInfo &itemInfo, const int size, bool reObtain)
{
    QString iconName;
    QIcon icon;
    bool findIcon = true;

    if (itemInfo.m_iconKey == "dde-calendar") {
        QString name = QStandardPaths::standardLocations(QStandardPaths::TempLocation).first() + "/"
                +  QString::number(QDate::currentDate().year())
                + "_" + QString::number(QDate::currentDate().dayOfYear()) + ".svg";

        if (!createCalendarIcon(name))
            iconName = itemInfo.m_iconKey;
        else
            iconName = name;
    } else {
        iconName = itemInfo.m_iconKey;
    }

    const qreal ratio = qApp->devicePixelRatio();
    const int iconSize = perfectIconSize(size);
    QPair<QString, int> tmpKey { cacheKey(itemInfo) , iconSize };

    do {
        if (iconName.startsWith("data:image/")) {
            const QStringList strs = iconName.split("base64,");
            if (strs.size() == 2)
                pixmap.loadFromData(QByteArray::fromBase64(strs.at(1).toLatin1()));

            if (!pixmap.isNull())
                break;
        }

        if (QFile::exists(iconName)) {
            if (iconName.endsWith(".ico"))
                // ico文件中是包含一组不同尺寸图标的容器文件, 需要根据不同iconSize获取对应尺寸的图标
                // 否则只能获取到最小的图标, 缩放时会出现锯齿
                pixmap = loadIco(iconName, qRound(iconSize * ratio));
            else
                pixmap = DHiDPIHelper::loadNxPixmap(iconName);

            if (!pixmap.isNull())
                break;
        }

        // 重新从主题中获取一次
        // 如果此提交我们使用的qt版本已经包含，那就可以不需要reObtain的逻辑了
        // https://codereview.qt-project.org/c/qt/qtbase/+/343396
        if (reObtain)
            icon = getIcon(iconName);
        else
            icon = QIcon::fromTheme(iconName);

        if (icon.isNull()) {
            icon = QIcon(":/widgets/images/application-x-desktop.svg");
            findIcon = false;
        }

        pixmap = icon.pixmap(QSize(iconSize, iconSize) * ratio);
        if (!pixmap.isNull())
            break;
    } while (false);

    pixmap = pixmap.scaled(QSize(iconSize, iconSize) * ratio, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    pixmap.setDevicePixelRatio(ratio);

    if (!IconCacheManager::existInCache(tmpKey) && findIcon)
        IconCacheManager::insertCache(tmpKey, pixmap);

    return findIcon;
}

/**
 * @brief getIcon 根据传入的\a name 参数重新从系统主题中获取一次图标
 * @param name 图标名
 * @return 获取到的图标
 * @note 之所以不使用QIcon::fromTheme是因为这个函数中有缓存机制，获取系统主题中的图标的时候，第一次获取不到，下一次也是获取不到
 */
QIcon getIcon(const QString &name)
{
    //TODO 这里找图标会耗时，界面轻微卡顿，后面可以改成单独开启线程去查找
    auto getIconList = [ = ] (const QString &iconName) {
        QProcess process;
        process.start("qtxdg-iconfinder", QStringList() << iconName);
        process.closeWriteChannel();
        process.waitForFinished();

        int exitCode = process.exitCode();
        QString outputTxt = process.readAllStandardOutput();

        auto list = outputTxt.split("\n");

        if (exitCode != 0 || list.size() <= 3)
            return QStringList() << "";

        // 去掉无用数据
        list.removeFirst();
        list.removeLast();
        list.removeLast();

        for (auto &s : list) {
            s = s.simplified();
        }

        return list;
    };

    return QIcon::fromTheme(getIconList(name).first());
}

QString cacheKey(const ItemInfo &itemInfo)
{
    return itemInfo.m_name + itemInfo.m_iconKey;
}

/**
 * @brief getDConfigValue 根据传入的\a key获取配置项的值，获取失败返回默认值
 * @param key 配置项键值
 * @param defaultValue 默认返回值，为避免出现返回值错误导致程序异常的问题，此参数必填
 * @param configFileName 配置文件名称
 * @return 配置项的值
 */
QVariant getDConfigValue(const QString &key, const QVariant &defaultValue, const QString &configFileName)
{
    QSharedPointer<DConfig> config(DConfig::create(DLauncher::DEFAULT_META_CONFIG_NAME, DLauncher::DEFAULT_META_CONFIG_NAME));
    if (!config->isValid()) {
        qWarning() << QString("DConfig is invalid, name:[%1], subpath[%2].").
                        arg(config->name(), config->subpath());
        return defaultValue;
    }

    if (config->keyList().contains(key))
        return config->value(key);

    return defaultValue;
}

bool isWaylandDisplay()
{
    return QGuiApplication::platformName().startsWith("wayland", Qt::CaseInsensitive);
}
