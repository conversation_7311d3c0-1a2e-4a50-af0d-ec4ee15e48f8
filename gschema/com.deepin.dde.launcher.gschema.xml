<?xml version="1.0" encoding="UTF-8"?>
<schemalist>
	<enum id="com.deepin.dde.launcher.DisplayMode">
		<value value="0" nick="free"/>
		<value value="1" nick="category"/>
	</enum>
	<schema path="/com/deepin/dde/launcher/" id="com.deepin.dde.launcher">
		<key name="display-mode" enum="com.deepin.dde.launcher.DisplayMode">
			<default>'free'</default>
			<summary>Launcher display mode.</summary>
			<description>Launcher display mode.</description>
		</key>
		<key name="fullscreen" type="b">
			<default>true</default>
		</key>
		<key name="auto-exit" type="b">
			<default>false</default>
		</key>
		<key name="apps-icon-ratio" type="d">
			<default>0.5</default>
		</key>
        	<key name='apps-hide-open-list' type='as'>
			<default>[]</default>
			<summary>apps hide function open</summary>
			<description>apps hide function open</description>
		</key>
        	<key name='apps-hide-send-to-desktop-list' type='as'>
			<default>[]</default>
			<summary>apps hide function send to desktop</summary>
			<description>apps hide function send to desktop</description>
		</key>
        	<key name='apps-hide-send-to-dock-list' type='as'>
			<default>[]</default>
			<summary>apps hide function send to dock</summary>
			<description>apps hide function send to dock</description>
		</key>
        	<key name='apps-hide-start-up-list' type='as'>
			<default>[]</default>
			<summary>apps hide function start up</summary>
			<description>apps hide function start up</description>
		</key>
        	<key name='apps-hide-uninstall-list' type='as'>
			<default>[]</default>
			<summary>apps hide function uninstall</summary>
			<description>apps hide function uninstall</description>
		</key>
        	<key name='apps-can-not-open-list' type='as'>
			<default>[]</default>
			<summary>apps not allowed to open</summary>
			<description>apps not allowed to open</description>
		</key>
        	<key name='apps-can-not-send-to-desktop-list' type='as'>
			<default>[]</default>
			<summary>apps not allowed to send to Desktop</summary>
			<description>apps not allowed to send to Desktop</description>
		</key>
        	<key name='apps-can-not-send-to-dock-list' type='as'>
			<default>[]</default>
			<summary>apps not allowed to send to Dock</summary>
			<description>apps not allowed to send to Dock</description>
		</key>
        	<key name='apps-can-not-start-up-list' type='as'>
			<default>[]</default>
			<summary>apps not allowed to start up</summary>
			<description>apps not allowed to start up</description>
		</key>
		<key name='apps-hide-use-proxy-list' type='as'>
			<default>[]</default>
			<summary>apps hide use proxy menu</summary>
			<description>apps hide use proxy menu</description>
		</key>
                <key name='apps-can-not-use-proxy-list' type='as'>
                        <default>[]</default>
                        <summary>apps disable use proxy menu</summary>
                        <description>apps disable use proxy menu</description>
                </key>
		<key name='apps-hold-list' type='as'>
			<default>['dde-introduction','dde-file-manager','deepin-appstore','deepin-app-store','deepin-terminal','deepin-manual','dde-computer','dde-trash','deepin-defender','dde-control-center','fcitx-config-gtk3','fcitx-configtool','deepin-system-monitor','deepin-devicemanager','dde-printer','dde-calendar','uos-service-support','deepin-toggle-desktop','deepin-wm-multitaskingview','kwin-wm-multitaskingview','com.deepin.store.intranet', 'chineseime-setting', 'uos-remote-assistance']</default>
			<summary>apps not allowed to uninstall</summary>
			<description>apps not allowed to uninstall</description>
		</key>
		<key name='apps-order' type='as'>
			<default>[]</default>
			<summary>launcher apps order</summary>
			<description>launcher apps order, ensure that all lowercase.</description>
		</key>
		<key name='apps-order-zh-cn' type='as'>
			<default>[]</default>
			<summary>launcher apps order</summary>
			<description>launcher apps order, ensure that all lowercase.</description>
		</key>
		<key name='apps-order-zh-tw' type='as'>
			<default>[]</default>
			<summary>launcher apps order</summary>
			<description>launcher apps order, ensure that all lowercase.</description>
		</key>

		<key name='apps-use-proxy' type='as'>
			<default>[]</default>
			<summary>apps use proxy</summary>
			<description></description>
		</key>

		<key name='apps-disable-scaling' type='as'>
			<default>[]</default>
			<summary>apps disable scaling</summary>
			<description></description>
		</key>

		<key name="apps-hidden" type='as'>
			<default>[]</default>
			<summary>apps hidden</summary>
			<description></description>
		</key>

		<key name="search-package-name" type='b'>
			<default>false</default>
			<summary>enable search package name</summary>
			<description></description>
		</key>
        	<key name='mini-frame-right-bar-hide-list' type='as'>
			<default>[]</default>
			<summary>icons not allowed to show on mini frame right bar</summary>
			<description>icons not allowed to show on mini frame right bar</description>
		</key>
	</schema>
  	<schema path="/com/deepin/dde/launcher/menu/" id="com.deepin.dde.launcher.menu" gettext-domain="DDE">
		<key name="open" type='b'>
			<default>true</default>
			<summary>enable open menu action</summary>
			<description></description>
		</key>
		<key name="send-to-desktop" type='b'>
			<default>true</default>
			<summary>enable send-to-desktop menu action</summary>
			<description></description>
		</key>
		<key name="send-to-dock" type='b'>
			<default>true</default>
			<summary>enable send-to-dock menu action</summary>
			<description></description>
		</key>
		<key name="auto-start" type='b'>
			<default>true</default>
			<summary>enable auto-start menu action</summary>
			<description></description>
		</key>
		<key name="uninstall" type='b'>
			<default>true</default>
			<summary>enable uninstall menu action</summary>
			<description></description>
		</key>
		<key name="use-proxy" type='b'>
			<default>true</default>
			<summary>enable use-proxy menu action</summary>
			<description></description>
		</key>
	</schema>
</schemalist>
