#include "sharedeventfilter.h"
#include "fullscreenframe.h"

#include <QEvent>
#include <QApplication>

#include "gtest/gtest.h"

class Tst_SharedEventFilter: public testing::Test
{
};

TEST_F(Tst_SharedEventFilter, eventFilter_test)
{
//    FullScreenFrame frame;
//    SharedEventFilter filter(&frame);

//    QKeyEvent event1(QEvent::KeyPress, Qt::Key_F1,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event1);

//    QKeyEvent event2(QEvent::KeyPress, Qt::Key_Enter,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event2);

//    QKeyEvent event3(QEvent::KeyPress, Qt::Key_Return,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event3);

//    QKeyEvent event4(QEvent::KeyPress, Qt::Key_Escape,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event4);

//    QKeyEvent event5(QEvent::KeyPress, Qt::Key_Space,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event5);

//    QKeyEvent event6(QEvent::KeyPress, Qt::Key_Tab,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event6);

//    QKeyEvent event7(QEvent::KeyPress, Qt::Key_Backtab,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event7);

//    QKeyEvent event8(QEvent::KeyPress, Qt::Key_Up,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event8);

//    QKeyEvent event9(QEvent::KeyPress, Qt::Key_Down,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event9);

//    QKeyEvent event10(QEvent::KeyPress, Qt::Key_Left,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event10);

//    QKeyEvent event11(QEvent::KeyPress, Qt::Key_Right,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event11);

//    QKeyEvent event12(QEvent::KeyPress, Qt::Key_Backspace,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event12);

//    QKeyEvent event13(QEvent::KeyPress, Qt::Key_P,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event13);

//    QKeyEvent event14(QEvent::KeyPress, Qt::Key_N,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event14);

//    QKeyEvent event15(QEvent::KeyPress, Qt::Key_F,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event15);

//    QKeyEvent event16(QEvent::KeyPress, Qt::Key_B,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event16);

//    QKeyEvent event17(QEvent::KeyPress, Qt::Key_Z,Qt::ControlModifier);
//    QApplication::sendEvent(&filter, &event17);
}
