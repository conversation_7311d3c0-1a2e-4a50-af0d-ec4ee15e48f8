Source: dde-launcher
Section: x11
Priority: optional
Maintainer: Deepin Packages Builder <<EMAIL>>
Build-Depends:
 debhelper-compat (= 11),
 pkg-config,
 cmake,
 qtbase5-dev,
 qtbase5-private-dev,
 qttools5-dev-tools,
 libqt5svg5-dev,
 libqt5x11extras5-dev,
 libxcb-ewmh-dev,
 libdframeworkdbus-dev (>=5.4.6),
 libdtkwidget-dev (>=5.4.19),
 libdtkcore-dev (>=5.4.14),
 libdtkgui-dev (>=5.4.13),
 libgsettings-qt-dev,
 libgtest-dev,
 libgmock-dev
Standards-Version: 3.9.8
Homepage: http://www.deepin.org/

Package: dde-launcher
Architecture: any
Depends:
 qtxdg-dev-tools,
 deepin-desktop-schemas (>=5.9.14),
 libdtkwidget5 (>=5.4.19),
 libdtkcore5 (>=5.4.14),
 libdtkgui5 (>=5.4.13),
 libdframeworkdbus2 (>=5.4.6),
 dde-daemon (>=5.13.12),
 startdde (>=5.8.9),
 lastore-daemon (>=5.2.9),
 libxcb-util0,
 ${misc:Depends},
 ${shlibs:Depends},
Conflicts:
 dde-workspace (<< 2.90.5),
Description:deepin desktop-environment - dde-launcher module
 Deepin desktop environment 2015 - dde-launcher module.
 Display all installation programs, and provide access to open, uninstall, boot and start, which is convenient for users to operate quickly