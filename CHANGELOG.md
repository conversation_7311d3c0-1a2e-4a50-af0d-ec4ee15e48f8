<a name="4.6.13"></a>
### 4.6.13 (2019-06-26)


#### Bug Fixes

*   remove unused deepin-qt5config ([ea4fdb37](https://github.com/linuxdeepin/dde-launcher/commit/ea4fdb3743b07bb057cb1b3190db11260c5240e7))



<a name="4.6.12"></a>
### 4.6.12 (2019-06-25)


#### Features

*   disable uninstall dde-introduction ([f7df3349](https://github.com/linuxdeepin/dde-launcher/commit/f7df3349c63bbc965f9e5c6945276ebbab8fbdc1))
*   disable uninstall for deepin-terminal ([ca3d622e](https://github.com/linuxdeepin/dde-launcher/commit/ca3d622e2b924442e0a4586af478f2aea418112e))



<a name="4.6.11"></a>
### 4.6.11 (2019-06-01)


#### Bug Fixes

* **Mini:**  unable to launch app in category list search ([cf8a936c](https://github.com/linuxdeepin/dde-launcher/commit/cf8a936c504dcdc87945228b8c7dd27a9484ada3))



<a name="4.6.10"></a>
### 4.6.10 (2019-05-10)


#### Bug Fixes

*   The coordinates of the entervent are incorrect after the menu is closed. ([e367c4b5](https://github.com/linuxdeepin/dde-launcher/commit/e367c4b5b8cf7c8b8e1ad0b91278b416031c5798))
* **FullScreen:**  category list coordinate error ([7344033a](https://github.com/linuxdeepin/dde-launcher/commit/7344033ad5332d8c53da739ad998b56aeb0af092))



<a name="4.6.9"></a>
### 4.6.9 (2019-04-09)


#### Bug Fixes

*   will flash on the sw platform ([e142543b](https://github.com/linuxdeepin/dde-launcher/commit/e142543ba4f3570c0794fc84d9213e73885121a5))



<a name="4.6.8"></a>
### 4.6.8 (2019-03-26)


#### Bug Fixes

*   Category heading is truncated ([2434217c](https://github.com/linuxdeepin/dde-launcher/commit/2434217c372fe7717a9f59a2934949658d0c8576))
* **Window:**  When the dock is on the right, the position is not close ([74cad49b](https://github.com/linuxdeepin/dde-launcher/commit/74cad49be6897aca8e2f695346a21f00f34e91b1))

#### Features

*   category button support dynamic font size ([e950b669](https://github.com/linuxdeepin/dde-launcher/commit/e950b669071c264e2c4396ce0e935a2acd4bed0d))



<a name="4.6.7"></a>
### 4.6.7 (2019-03-05)


#### Bug Fixes

*   backspace do not work in window mode search edit ([e983ffc7](https://github.com/linuxdeepin/dde-launcher/commit/e983ffc70df79f0b270c830a239f4631902c388f))
* **Box:**  search result page has no height adjustment ([9cd2d72b](https://github.com/linuxdeepin/dde-launcher/commit/9cd2d72b0cb872a18f6702d03095093798ad0c5a))



<a name="4.6.6"></a>
## 4.6.6 (2019-02-26)


#### Bug Fixes

*   dragging icon size error in fullscreen frame ([e68a0439](https://github.com/linuxdeepin/dde-launcher/commit/e68a043926f406ac473090aa0aaf3c0de2cb5732))



<a name="4.6.5"></a>
### 4.6.5 (2019-02-26)


#### Bug Fixes

*   show launcher on lock ([5d2d44c0](https://github.com/linuxdeepin/dde-launcher/commit/5d2d44c012cd97d02c62d554bfc50168f3730148))
*   compile-time option to disable uninstall ([03bd8a91](https://github.com/linuxdeepin/dde-launcher/commit/03bd8a9126e2164b2ccbd7c3dfac17f92bdf7cdd))
* **Background:**  gradient not update when background changed ([11fe6a11](https://github.com/linuxdeepin/dde-launcher/commit/11fe6a11b7f424642bcab3b283b278a655072c03))



<a name="4.6.4"></a>
### 4.6.4 (2019-01-25)


#### Bug Fixes

* **FullScreen:**  not set origin size ([9d517817](https://github.com/linuxdeepin/dde-launcher/commit/9d5178179a379608c0fc04a89f65422416606423))



<a name="4.6.3"></a>
### 4.6.3 (2019-01-14)


#### Bug Fixes

*   delete selected text when append new char to search bar ([ee2a071b](https://github.com/linuxdeepin/dde-launcher/commit/ee2a071b3a223136dd9c6f78343ec0e5dba33365))
* **Mini:**  replace search widget ([03cd5170](https://github.com/linuxdeepin/dde-launcher/commit/03cd5170b6cfee7f00da3a47c0ccc54bff27b959))



<a name="4.6.2"></a>
### 4.6.2 (2018-12-28)


#### Bug Fixes

* **Mini:**  cannot enter category when not scroll view ([269276d1](https://github.com/linuxdeepin/dde-launcher/commit/269276d19886701318962e333b61eed9b1e13a90))



<a name="4.6.1"></a>
### 4.6.1 (2018-12-27)


#### Bug Fixes

* **Mini:**  compatible with Qt5.6 ([5efd2925](https://github.com/linuxdeepin/dde-launcher/commit/5efd2925723d14f0f12b52b356e00673d5050318))



<a name=""></a>
##  4.6.0 (2018-12-27)


#### Bug Fixes

*   optimized application list sorting algorithm on mini mode ([a3b6962e](https://github.com/linuxdeepin/dde-launcher/commit/a3b6962ed0ebc7d61b31df5458e391b601c45d91))
* **background:**  refresh background ([e9a6c4bb](https://github.com/linuxdeepin/dde-launcher/commit/e9a6c4bbf08226caf997b4a88a4b1c3d990adabd))

#### Features

*   update scroll interval ([0371f517](https://github.com/linuxdeepin/dde-launcher/commit/0371f517a274fe3591cf9eb607d96049093c3032))
* **mini:**  auto change font size ([288d123d](https://github.com/linuxdeepin/dde-launcher/commit/288d123dc7e97f2f69c6dd39e02d3b50f8fbe075))



<a name="4.5.9"></a>
### 4.5.9 (2018-12-19)


#### Features

* **fullscreen:**  support kwin ([105aa33e](https://github.com/linuxdeepin/dde-launcher/commit/105aa33e7e3bfa29e59f7facc1d9c725452eadd2))

#### Bug Fixes

*   focus keyboard switch. ([0e6e7857](https://github.com/linuxdeepin/dde-launcher/commit/0e6e785737c2b0883a2b69b9f5f49ee354871b3a))



<a name="4.5.8"></a>
### 4.5.8 (2018-12-18)


#### Bug Fixes

*   add DTK version check ([6b697f67](https://github.com/linuxdeepin/dde-launcher/commit/6b697f67b6b12af54b83b813fe2be997bd06ec24))



<a name="4.5.7"></a>
### 4.5.7 (2018-12-18)


#### Bug Fixes

* **Mini:**  use DWindowManagerHelper::currentWorkSpaceWindowIdList ([9a75d655](https://github.com/linuxdeepin/dde-launcher/commit/9a75d6559116c8ee9c28a7824be411bcc890f8f5))



<a name=""></a>
##  4.5.6 (2018-12-17)


#### Bug Fixes

* **Mini:**  stop the inertial scrolling of the head and bottom ([e0fec2f6](https://github.com/linuxdeepin/dde-launcher/commit/e0fec2f6cd26a1b60f32ebacd21e85ea0ee7c73b))



<a name=""></a>
##  4.5.5.2 (2018-12-12)


#### Bug Fixes

*   cannot launcher app ([77fe8dee](https://github.com/linuxdeepin/dde-launcher/commit/77fe8dee68bcd3085dd5ef7a03cc0b4720127721))



<a name=""></a>
##  4.5.5.1 (2018-12-12)


#### Bug Fixes

* **Mini:**  cannot click ([91675f5e](https://github.com/linuxdeepin/dde-launcher/commit/91675f5e7c5ce39209ba616eb72185b77ae24494))



<a name="4.5.5"></a>
### 4.5.5 (2018-12-11)




<a name="4.5.4"></a>
### 4.5.4 (2018-12-07)


#### Features

*   support touch scroll ([523b1feb](https://github.com/linuxdeepin/dde-launcher/commit/523b1feb0638f7fe23ee6519df8a873b368d8c55))
*   check deepin-manual ([0b8b5a32](https://github.com/linuxdeepin/dde-launcher/commit/0b8b5a32777d6f1deba9136027587888a2ecc4a8))
* **Mini:**  don't hide when using virtual keyboard ([a7eef873](https://github.com/linuxdeepin/dde-launcher/commit/a7eef8739e340c60bcf25963f0bc3d8d74a1d13a))



<a name="4.5.3"></a>
### 4.5.3 (2018-11-30)


#### Features

* **Mini:**  average startup number sort ([ad4e14b4](https://github.com/linuxdeepin/dde-launcher/commit/ad4e14b4b727917f086e949f381f6945d7caf81f))

#### Bug Fixes

* **Mini:**  disable verticalScroll context menu ([9ade7a8e](https://github.com/linuxdeepin/dde-launcher/commit/9ade7a8ecce0942a52238c624f5fdc734682d5f4))



<a name="4.5.2"></a>
### 4.5.2 (2018-11-09)


#### Bug Fixes

*   tips label is not hidden. ([ef0072d9](https://github.com/linuxdeepin/dde-launcher/commit/ef0072d9fc0b647433a7d7c196674166ba9c8a8b))



<a name="4.5.1"></a>
### 4.5.1 (2018-11-01)


#### Bug Fixes

*   auto exit ([f73862ca](https://github.com/linuxdeepin/dde-launcher/commit/f73862ca23d8d690613021897014f2f11b8291a6))
*   scroll down is very short ([9870f39f](https://github.com/linuxdeepin/dde-launcher/commit/9870f39f8e39888d8d6302a447f36c1645a15406))
* **Mini:**
  *  cannot hide ([c2fee131](https://github.com/linuxdeepin/dde-launcher/commit/c2fee1311c91642b80dbd5eca594cb690a46c2ea))
  *  Multi-screen coordinates ([78328987](https://github.com/linuxdeepin/dde-launcher/commit/78328987a1e84c70533d5ab8cc3ffebbda0ace5a))
* **windowed_frame:**  width calculation is incorrect. ([df5d56e9](https://github.com/linuxdeepin/dde-launcher/commit/df5d56e93c3168fc4599ba34bd05b1d5bd9b5b2a))

#### Features

*   add regionMonitorPoint interface ([d0aff55a](https://github.com/linuxdeepin/dde-launcher/commit/d0aff55a2c5aa06ae597918ab80019389b95384c))
* **Mini:**  support vertical scrollbar show as needed ([c9d581da](https://github.com/linuxdeepin/dde-launcher/commit/c9d581daef9b754a14d4eceac2425440e8050b51))



<a name=""></a>
##  4.5.0 (2018-10-25)


#### Features

*   support back tab key ([69bf3e71](https://github.com/linuxdeepin/dde-launcher/commit/69bf3e7155e5b775ca0517aeeae7bfd7bf0b1f20))
*   support change frame opacity ([8f715f8e](https://github.com/linuxdeepin/dde-launcher/commit/8f715f8ed4f33351310c5305e1392affa4cb9db1))
*   disable animation when scroll for sw ([dd2e4d75](https://github.com/linuxdeepin/dde-launcher/commit/dd2e4d75a6ec61daba86bc803e01476bfaf500d7))
*   optimization switch mode ([3648a8f4](https://github.com/linuxdeepin/dde-launcher/commit/3648a8f455dd7e46c2f807ef72a467b2069914a1))
*   support Ctrl+V shortcuts to pasted text to the search widget ([9de76fef](https://github.com/linuxdeepin/dde-launcher/commit/9de76fef10012a6ebe11802500a640c680db86ef))
* **windowed_frame:**  adaptive window width. ([5cb86144](https://github.com/linuxdeepin/dde-launcher/commit/5cb86144eaace4cb9e0851a9c741edc73ec684fd))

#### Bug Fixes

*   content highly adaptable to dock's position ([c4d65ffa](https://github.com/linuxdeepin/dde-launcher/commit/c4d65ffa177f013a363c4e712070ddf37242b0e6))
*   mission disable animation macro ([579b9a48](https://github.com/linuxdeepin/dde-launcher/commit/579b9a4804064163090b30c4df98416966d6f185))
*   background shift ([b5aea889](https://github.com/linuxdeepin/dde-launcher/commit/b5aea889a1614eb5f3b63d80337982d96d18bb76))
*   geometry error ([d91b45b0](https://github.com/linuxdeepin/dde-launcher/commit/d91b45b0c01024510e4225f00b2fa357fff23a92))
*   don't show when not have app infos ([2d88823c](https://github.com/linuxdeepin/dde-launcher/commit/2d88823c00f3fbf434e560aa21f2ff76d60c10a7))
*   Incorrect return reference ([aa8eb95e](https://github.com/linuxdeepin/dde-launcher/commit/aa8eb95e2fba2cf496e5c5f5ddc9cc44a79bde0a))
*   check all app info is vaild ([81b07fc2](https://github.com/linuxdeepin/dde-launcher/commit/81b07fc222cc7d24d6582920b346af6157caee40))
*   current index is incorrect ([8690239b](https://github.com/linuxdeepin/dde-launcher/commit/8690239b29bc6613606bf573c982be4262fe21ee))
* **FullScreen:**
  *  size error when dock's position is right ([6bac4efe](https://github.com/linuxdeepin/dde-launcher/commit/6bac4efedb5792bca25acbfe10929aa7d9579bdd))
  *  draw background error after changing the dock position ([4bd00c20](https://github.com/linuxdeepin/dde-launcher/commit/4bd00c2011b0ea3e0878da0583ae0a5902246ee7))
* **Mini:**  double hide launcher ([3a129bc5](https://github.com/linuxdeepin/dde-launcher/commit/3a129bc5efde20ee71fb35076a480b9f1ed562b7))
* **sw:**  mission mieee ([2291a99f](https://github.com/linuxdeepin/dde-launcher/commit/2291a99f256e32b8307da46eb16a8e42b9a61406))
* **windowed:**  more perfect scrolling. ([1c5ffd9b](https://github.com/linuxdeepin/dde-launcher/commit/1c5ffd9b8f5c94c9d9500dcc56ce6625a8ae3f17))



<a name=""></a>
##  4.4.5.6 (2018-09-26)


#### Bug Fixes

*   mission disable animation macro ([8d832b68](https://github.com/linuxdeepin/dde-launcher/commit/8d832b683929a7c4c7367c65fbce42f6aa85c09f))

#### Features

*   disable animation when scroll for sw ([1d5b235a](https://github.com/linuxdeepin/dde-launcher/commit/1d5b235aca33ce5a098d986acd59a547ad6c3e79))



<a name=""></a>
##  4.4.5.5 (2018-09-20)




<a name=""></a>
##  4.4.5.4 (2018-09-07)


#### Bug Fixes

*   background shift ([cd29ce9d](https://github.com/linuxdeepin/dde-launcher/commit/cd29ce9d61db15142aa808241cea6c68b799788b))



<a name=""></a>
##  4.4.5.3 (2018-09-03)


#### Bug Fixes

*   Incorrect return reference ([08a23f93](https://github.com/linuxdeepin/dde-launcher/commit/08a23f9387f337483ac365140a9640877d08f1d2))
* **FullScreen:**  draw background error after changing the dock position ([ace96c82](https://github.com/linuxdeepin/dde-launcher/commit/ace96c820547eb27de28fbf8dcd34f4716b4f52b))

#### Features

*   optimization switch mode ([0fa53198](https://github.com/linuxdeepin/dde-launcher/commit/0fa531985fcea2cbe4023c7fc2be7c8abfc885f9))



<a name=""></a>
##  4.4.5.2 (2018-08-31)


#### Bug Fixes

*   check all app info is vaild ([0c860c02](https://github.com/linuxdeepin/dde-launcher/commit/0c860c0254b609dd5cb88aac93468c31cd37b303))
* **sw:**  mission mieee ([27d755ae](https://github.com/linuxdeepin/dde-launcher/commit/27d755aed14c4bddb1cb4c712b553e9862fd91e8))



<a name=""></a>
##  4.4.5.1 (2018-08-28)


#### Bug Fixes

*   don't show when not have app infos ([6d85a527](https://github.com/linuxdeepin/dde-launcher/commit/6d85a527be7539ce815f990bc9142b1c8524bff6))



<a name="4.4.5"></a>
### 4.4.5 (2018-08-12)


#### Bug Fixes

* **switch_btn:**  clear check status when clicked. ([04bda128](https://github.com/linuxdeepin/dde-launcher/commit/04bda1288c57e29e1659d76cafbede35bd5765ad))



<a name="4.4.4"></a>
### 4.4.4 (2018-08-07)


#### Features

*   tab selected category button. ([c8ca2e12](https://github.com/linuxdeepin/dde-launcher/commit/c8ca2e1244cf2c1384662bb0cd481dc7e85c43fe))
* **Mini:**  update hover color ([9232ef1e](https://github.com/linuxdeepin/dde-launcher/commit/9232ef1e5f50d794873daff4bad53fb5e07aaa93))

#### Bug Fixes

*   no effect selected when scrolling. ([12cbc38c](https://github.com/linuxdeepin/dde-launcher/commit/12cbc38c72901798c7cb18afed3cebec2a4228a1))
* **Mini:**  not draw background when leave list again ([15d9bcc7](https://github.com/linuxdeepin/dde-launcher/commit/15d9bcc7ef312a9c8c6676d1bb0ce40ba13cd112))



<a name="4.4.3"></a>
### 4.4.3 (2018-08-01)


#### Bug Fixes

*   No translation of static variables ([7356afc1](https://github.com/linuxdeepin/dde-launcher/commit/7356afc11b926004e7e291e23f73fdb9cdbed560))



<a name="4.4.2"></a>
### 4.4.2 (2018-07-31)


#### Bug Fixes

* **Mini:**
  *  repeat generation of category list ([1b1ea389](https://github.com/linuxdeepin/dde-launcher/commit/1b1ea389b1fe2b02426ab37fead938352d5b21c0))
  *  mini mode and full screen mode separation ([eeb31e5c](https://github.com/linuxdeepin/dde-launcher/commit/eeb31e5c50cc73f46675a1fa694cc1611deea631))

#### Features

* **Mini:**
  *  hidden empty category ([31b889cc](https://github.com/linuxdeepin/dde-launcher/commit/31b889ccf50211f3b39a6ae0669ce473e8b72c90))
  *  change right buttons order ([f925b9be](https://github.com/linuxdeepin/dde-launcher/commit/f925b9be163002be52b73803e05cb7cee833e8dd))



<a name="4.4.1"></a>
### 4.4.1 (2018-07-20)


#### Bug Fixes

* **Mini:**  no hover state in the search mode ([e1ca8678](https://github.com/linuxdeepin/dde-launcher/commit/e1ca8678b78212dee576ae11b38b52b4043a2992))



<a name=""></a>
##  4.4.0 (2018-07-20)


#### Bug Fixes

* **Mini:**
  *  Read the wrong classification information ([d0e1158f](https://github.com/linuxdeepin/dde-launcher/commit/d0e1158fb49b2f4d4b919659015af087acb7373e))
  *  icon error ([aadbc3fe](https://github.com/linuxdeepin/dde-launcher/commit/aadbc3fe14b8afce34134d528c51e71fd5e79b18))
  *  set datetime contents margins ([ceaa522c](https://github.com/linuxdeepin/dde-launcher/commit/ceaa522c1dbdd132ca00ddc05f504dd7f5a4b757))
  *  right menu not popup ([cdc1a120](https://github.com/linuxdeepin/dde-launcher/commit/cdc1a1208d6912e82401e40773e6d88516fe52bb))

#### Features

*   remove message of the uninstall dialog ([0b8e3205](https://github.com/linuxdeepin/dde-launcher/commit/0b8e3205fac54ea54479ed2c6e7c646ab76adf97))
* **Mini:**
  *  not draw background when list view not focus ([ca6c120f](https://github.com/linuxdeepin/dde-launcher/commit/ca6c120f559a22bbb579b87df6031344a725f694))
  *  update ts of switch button ([c17dcd51](https://github.com/linuxdeepin/dde-launcher/commit/c17dcd5186d13d5f9bbcaa5cce1be9bed4ed2625))
  *  support HiDPI ([686bd847](https://github.com/linuxdeepin/dde-launcher/commit/686bd8479372a3ff1c8a692b080cba0378131f59))
  *  support use of the direction key ([9ff2ffab](https://github.com/linuxdeepin/dde-launcher/commit/9ff2ffabea194421fcd7eaec5d25c734d0d3915f))
  *  add back button ([33d5629b](https://github.com/linuxdeepin/dde-launcher/commit/33d5629be2452ab2bd0e455a3a8e8a17d1bc3eba))
  *  support switch to cagegory ([6ac8b357](https://github.com/linuxdeepin/dde-launcher/commit/6ac8b357283cdcb262bb8b0deda366bf4fb40551))
  *  draw catelate enter icon ([41a6c2d4](https://github.com/linuxdeepin/dde-launcher/commit/41a6c2d41e821201d6c6c3bdcf5bc8bb9f6b0092))
  *  create category list ([18f4a439](https://github.com/linuxdeepin/dde-launcher/commit/18f4a4398c3e782da8ebc6bbb3a2109bb1d03980))



<a name="4.3.10"></a>
### 4.3.10 (2018-06-27)


#### Bug Fixes

* **background:**  cannot get blur wallpaper ([69486c62](https://github.com/linuxdeepin/dde-launcher/commit/69486c627f8417ad969831e627af9da6f7d187be))



<a name="4.3.9"></a>
### 4.3.9 (2018-06-27)


#### Bug Fixes

* **Mini:**
  *  time overlaps in different fonts ([4456de02](https://github.com/linuxdeepin/dde-launcher/commit/4456de028812ddbcd2c65ca0c8d5d57c55de534d))
  *  Drag and drop coordinate errors after window scale ([b6c3d47a](https://github.com/linuxdeepin/dde-launcher/commit/b6c3d47a560801a2e9084c2638921fb991fb5d07))



<a name="4.3.8"></a>
### 4.3.8 (2018-06-20)


#### Bug Fixes

*   compatible processing ([d01d447f](https://github.com/linuxdeepin/dde-launcher/commit/d01d447f8592a0c1bd1446783e788db91939d80f))
* **Mini:**  conflict with mouse hover, block signal ([48ead6de](https://github.com/linuxdeepin/dde-launcher/commit/48ead6deae0ec4e3f0b0558f477368f024bb9f5e))



<a name="4.3.7"></a>
### 4.3.7 (2018-06-20)


#### Bug Fixes

* **Mini:**  widgets qrc lost ([96e6f24e](https://github.com/linuxdeepin/dde-launcher/commit/96e6f24e182ec7e03824668b4ce858ef83c5eac4))



<a name="4.3.6"></a>
### 4.3.6 (2018-06-14)


#### Bug Fixes

* **Mini:**  check the list of users'use ([e9ff3873](https://github.com/linuxdeepin/dde-launcher/commit/e9ff3873b3a253d3b1e54b763309a8b92d4e2c28))



<a name="4.3.5"></a>
### 4.3.5 (2018-06-12)


#### Bug Fixes

* **Mini:**
  *  uninstalled just installed app, tips still exist ([99261d81](https://github.com/linuxdeepin/dde-launcher/commit/99261d8116dc32fc190125642ef59e7cf15158fd))
  *  reduce scroll speed ([a104a810](https://github.com/linuxdeepin/dde-launcher/commit/a104a8106129100b55d63e9de0e217e8b73a4ca2))
  *  stop hidden timer when hidden ([ba0ace41](https://github.com/linuxdeepin/dde-launcher/commit/ba0ace4161c7aaef3d935967f6954aae56a4491f))



<a name=""></a>
##  4.3.4 (2018-06-07)


#### Bug Fixes

* **Mini:**
  *  no prompt for new installation ([997f0cc8](997f0cc8))
  *  delete rolling restrictions ([7c7fc185](7c7fc185))
* **mini:**  disable right button click on avatar ([61ab297b](61ab297b))

#### Features

* **Mini:**
  *  recovery all state when frame hide ([6c3edb46](6c3edb46))
  *  update new style ([8193493c](8193493c))

<a name=""></a>
##  ******* (2018-05-29)

#### Bug Fixes

*   drop hotspot support hidpi ([1521c978](1521c978))
*   used list error. ([7e991834](7e991834))
*   hot spot invalid. ([7760af39](7760af39))
*   optimize rolling speed. ([a1fdb3c4](a1fdb3c4))
*   unified dialog icon size. ([b44378ff](b44378ff))
*   tips rect has no vertical. ([f9bf57ce](f9bf57ce))
*   2D only draw rect. ([2061a3b2](2061a3b2))
*   modify display jump btn logic. ([6633542c](6633542c))

#### Features

*   add scroll animation. ([0139257f](0139257f))


<a name=""></a>
##  4.3.3 (2018-05-23)


#### Features

*   optimize the window mode border. ([872ed8e2](872ed8e2))
*   append new item will show the jump button. ([8412e1a4](8412e1a4))
*   keep align horizontal center ([0d3656e9](0d3656e9))
*   adjust ui. ([c63390e4](c63390e4))
*   drop pixmap add shadow effect. ([92e0e75f](92e0e75f))
*   optimize the drag effect. ([1129b6a2](1129b6a2))
*   support drag and drop. ([7ae725de](7ae725de))
*   jump to control-center when click time & date label ([2801928f](2801928f))
*   button to increase image ([18e13292](18e13292))
*   open control-center account settings when click avatar ([e4ed2169](e4ed2169))
*   add rounded button. ([725bcbbf](725bcbbf))
*   add the separator between searchWidget and appsview. ([3a974e20](3a974e20))
*   save the used list. ([09d400c8](09d400c8))
*   the new installation tips ([26e4f082](26e4f082))
*   save used list config & fix switch model incorrect ([5b747876](5b747876))
*   sort used data model ([6ea47f7c](6ea47f7c))
*   add switch button to new frame ([6dbb3222](6dbb3222))
*   add used model ([5c5de36d](5c5de36d))
*   add date time to NewFrame ([dd7df8bf](dd7df8bf))
*   add new frame ([3dd096d1](3dd096d1))
*   read auto-exit settings ([3da8c20b](3da8c20b))
*   hold deepin-manual, forbid uninstall ([d8ac5134](d8ac5134))
*   add auto-exit timer ([7a065508](7a065508))
*   change drag pixmap opacity ([582cf793](582cf793))
*   support mouse wheel to adjust icon size ([874b375e](874b375e))
* **NewFrame:**
  *  add search tips label ([418cc845](418cc845))
  *  add right menu ([fc85a8bb](fc85a8bb))
  *  add right bar & search widget. ([066f430f](066f430f))
* **mini:**  hide frame when popup uninstall dialog ([6248e7c6](6248e7c6))
* **rightBar:**  respond to each button ([14bb6686](14bb6686))
* **windowed_frame:**  search support input method. ([010669f9](010669f9))

#### Bug Fixes

*   unified dialog icon size. ([b44378ff](b44378ff))
*   tips rect has no vertical. ([f9bf57ce](f9bf57ce))
*   2D only draw rect. ([2061a3b2](2061a3b2))
*   modify display jump btn logic. ([6633542c](6633542c))
*   block tab key ([1322d4d3](1322d4d3))
*   disable drop animation ([7b5b1084](7b5b1084))
*   new tips not vertical center. ([ca396f5b](ca396f5b))
*   wm support transparent to draw a shadow effect. ([557daa6f](557daa6f))
*   drag indicator pixmap support HiDPI. ([a32b6b92](a32b6b92))
*   use apt remove app the list did not deleted. ([a8c75ef9](a8c75ef9))
*   don't swap places when rolling. ([7bde0835](7bde0835))
*   incorrect drag start pos ([70de7dfd](70de7dfd))
*   used list does not append new app & remove app item(s). ([cebb07ee](cebb07ee))
*   drag start index error ([a412eee1](a412eee1))
*   no refresh when uninstalled for used model ([6980d1df](6980d1df))
*   switch error & refactor switchbtn ([2936830a](2936830a))
*   optimize created apps do not refresh list ([288e24f8](288e24f8))
*   not auto quit ([f8e8d082](f8e8d082))
*   Not scrolling enlarge at the top ([3716f2e7](3716f2e7))
*   gsettings key error ([ebde0e07](ebde0e07))
*   Adapt lintian ([8e5ff24f](8e5ff24f))
*   window position error in mini mode ([eb0310c6](eb0310c6))
*   autostart icon disappear ([35f2129b](35f2129b))
*   show paint error when show_detail ([b06bcd50](b06bcd50))
*   not display chainsproxy when not configuration ([6ef7f873](6ef7f873))
* **WindowFrame:**  use DRegionMonitor check mouse point ([9f59c7ac](9f59c7ac))
* **autostart:**  autostart mark indicator position error in HiDPI ([f18937a1](f18937a1))
* **background:**
  *  fallback to default when wallpaper not found ([cb018a6e](cb018a6e))
  *  not update gradient when background blur changed ([dde22974](dde22974))
  *  use blur interface ([af054c2f](af054c2f))
* **listview:**  right click is not selected ([196b111d](196b111d))
* **menu:**  Memory leak, no destruction of all objects. ([54c54b69](54c54b69))
* **miniframe:**  can move by mouse ([07930233](07930233))
* **view:**  item not update when move to new one ([17e5a66b](17e5a66b))



<a name="4.3.2"></a>
### 4.3.2 (2018-03-16)


#### Bug Fixes

* **autostart:**  autostart mark indicator position error in HiDPI ([f18937a1](f18937a1))



<a name="4.3.1"></a>
## 4.3.1 (2018-03-08)


#### Features

*   add auto-exit timer ([7a065508](7a065508))
*   change drag pixmap opacity ([582cf793](582cf793))
*   support mouse wheel to adjust icon size ([874b375e](874b375e))
* **mini:**  hide frame when popup uninstall dialog ([6248e7c6](6248e7c6))

#### Bug Fixes

*   Adapt lintian ([8e5ff24f](8e5ff24f))
*   window position error in mini mode ([eb0310c6](eb0310c6))
*   autostart icon disappear ([35f2129b](35f2129b))
*   show paint error when show_detail ([b06bcd50](b06bcd50))
*   not display chainsproxy when not configuration ([6ef7f873](6ef7f873))
* **background:**
  *  fallback to default when wallpaper not found ([cb018a6e](cb018a6e))
  *  not update gradient when background blur changed ([dde22974](dde22974))
  *  use blur interface ([af054c2f](af054c2f))
* **menu:**  Memory leak, no destruction of all objects. ([54c54b69](54c54b69))
* **miniframe:**  can move by mouse ([07930233](07930233))
* **view:**  item not update when move to new one ([17e5a66b](17e5a66b))



### 4.3.0
* Fix position error.

### 4.2.9
* Update translate files.

### 4.2.8
* Add menu option to disable HiDPI-scale on application.

### 4.2.7
* Fix non-integer HiDPI-scale 1px black line.

### 4.2.6
* Improve HiDPI painting

### 4.2.5
* Update translations.

### 4.2.4
* Improve item icon adjust policy.
  Improve graphics paint algorithm.

### 4.2.3
* Fix: uninstll dialog icon too smaill at HiDPI screen.

### 4.2.2
* Feature: Hide frame when uninstall dialog popup.
* Fix: startup mark icon placed pos error

### 4.2.1
* Fix autostart icon disappear

### 4.2.0
* HiDPI Support

### 4.1.9
* Minor bug fixes
