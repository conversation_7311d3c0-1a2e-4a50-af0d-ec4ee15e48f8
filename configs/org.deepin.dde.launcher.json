{"magic": "dsg.config.meta", "version": "1.0", "contents": {"useSolidBackground": {"value": false, "serial": 0, "flags": [], "name": "UseSolidBackground", "name[zh_CN]": "使用纯色背景", "description": "是否使用纯色背景，默认为否。如果使用了纯色背景，将不会加载背景图片，降低内存占用。", "permissions": "readwrite", "visibility": "private"}, "preloadAppsIcon": {"value": true, "serial": 0, "flags": [], "name": "PreloadAppsIcon", "name[zh_CN]": "是否预加载应用图标", "description": "是否预加载应用的图标，默认为是。开启设置时，将在程序启动时加载所有的应用图标，避免切换界面时加载图标，使切换页面更加流畅。关闭设置时，程序启动时不会加载图标，当需要使用某个应用图标的时候才会去加载，降低内存占用，但是如果机器性不佳，可能造成切换界面卡顿的现象。", "permissions": "readwrite", "visibility": "private"}, "enableFullScreenMode": {"value": true, "serial": 0, "flags": [], "name": "EnableFullScreenMode", "name[zh_CN]": "是否允许使用全屏模式", "description": "是否允许使用全屏模式，默认为是。开启配置时，用户可以通过模式切换按钮将启动器设置为全屏或者窗口模式。关闭配置时，程序隐藏模式切换按钮，且默认为窗口模式，用户无法切换到全屏模式。", "permissions": "readwrite", "visibility": "private"}}}